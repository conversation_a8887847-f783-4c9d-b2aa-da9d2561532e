import pymysql
from contextlib import contextmanager
import pymysql.cursors
import logging
from constants import config_manager

logger = logging.getLogger('DatabaseManager')

class DatabaseManager:
    """
    Manages database operations for the live_data table.
    Provides connection handling, query execution, and table initialization.
    """

    def __init__(self, host: str = None, user: str = None, password: str = None, database: str = None):
        self.host = host or config_manager.database.HOST
        self.user = user or config_manager.database.USER
        self.password = password or config_manager.database.PASSWORD
        self.database = database or config_manager.database.DATABASE
        self.table_name = config_manager.database.TABLE
       

    @contextmanager
    def get_connection(self):
        """
        Context manager that yields a database connection from pool.
        Ensures the connection is properly returned to the pool after use.
        """
        conn = None
        try:
            conn = self._get_connection_from_pool()
            yield conn
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            yield None
        finally:
            if conn:
                self._return_connection_to_pool(conn)

    def initialize(self) -> None:
        """
        Creates the live_data table if it does not already exist.
        And adds necessary indexes for better performance.
        """
        try:
            with self.get_connection() as conn:
                if not conn:
                    logger.error("Connection not established; cannot create table.")
                    return
                with conn.cursor() as cursor:
                    create_table_sql = f"""
                    CREATE TABLE IF NOT EXISTS {self.table_name} (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        username VARCHAR(255) NOT NULL,
                        viewer_count DOUBLE,
                        status VARCHAR(50),
                        link VARCHAR(500),
                        timestamp DATETIME,
                        message_log VARCHAR(255),
                        assigned_to INT DEFAULT NULL,
                        sorgu_tarihi DATETIME
                    ) ENGINE=INNODB DEFAULT CHARSET=utf8mb4
                    """
                    cursor.execute(create_table_sql)
                
                # Önce indekslerin varlığını kontrol edelim
                index_checks = [
                    ("idx_username", "username"),
                    ("idx_status", "status"), 
                    ("idx_timestamp", "timestamp")
                ]
                
                for index_name, column_name in index_checks:
                    # Önce indeksin var olup olmadığını kontrol et
                    check_query = f"""
                    SELECT COUNT(*) as cnt
                    FROM INFORMATION_SCHEMA.STATISTICS
                    WHERE table_schema = DATABASE()
                    AND table_name = '{self.table_name}'
                    AND index_name = '{index_name}'
                    """
                    
                    with conn.cursor() as cursor:
                        cursor.execute(check_query)
                        result = cursor.fetchone()
                        
                    if result and result['cnt'] == 0:
                        # İndeks yoksa oluştur
                        try:
                            create_index_query = f"CREATE INDEX {index_name} ON {self.table_name} ({column_name})"
                            with conn.cursor() as cursor:
                                cursor.execute(create_index_query)
                            
                        except Exception as e:
                            # Burada sadece "zaten var" hatalarını görmezden gelelim
                            if "Duplicate" not in str(e):
                                logger.error(f"Index oluşturma hatası: {e}")       
                conn.commit()
              
        except Exception as e:
            logger.error(f"Error creating table: {e}")
    
    
    def check_connection(self):
        """Veritabanı bağlantısını kontrol eder ve sonucu cache'ler."""
        if hasattr(self, '_connection_ok'):
            return self._connection_ok
            
        with self.get_connection() as conn:
            if conn:
                self._connection_ok = True
                return True
            else:
                self._connection_ok = False
                return False
            
    def get_statistics(self, period="today"):
        """
        Belirli bir zaman periyodu için istatistik verileri döndürür.
        
        Args:
            period: Zaman periyodu ("today", "week", "month", "all")
            
        Returns:
            İstatistikleri içeren bir sözlük.
        """
        where_clause = ""
        params = ()
        
        if period == "today":
            where_clause = "WHERE DATE(timestamp) = CURDATE()"
        elif period == "week":
            where_clause = "WHERE timestamp >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)"
        elif period == "month":
            where_clause = "WHERE timestamp >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)"
        
        query = f"""
        SELECT 
            COUNT(*) as total_users,
            SUM(CASE WHEN status LIKE 'Uygun%%' THEN 1 ELSE 0 END) as eligible_users,
            SUM(CASE WHEN status = 'Uygun değil' THEN 1 ELSE 0 END) as ineligible_users,
            SUM(CASE WHEN message_log = 'Mesaj Gönderildi' THEN 1 ELSE 0 END) as messages_sent,
            AVG(viewer_count) as avg_viewers,
            MAX(viewer_count) as max_viewers
        FROM {self.table_name}
        {where_clause}
        """
        
        result = self.execute_query(query, params)
        if not result:
            return {
                "total_users": 0,
                "eligible_users": 0,
                "ineligible_users": 0,
                "messages_sent": 0,
                "avg_viewers": 0,
                "max_viewers": 0
            }
        
        stats = result[0]
        
        # None değerleri 0'a çevir
        for key in stats:
            if stats[key] is None:
                stats[key] = 0
        
        return stats
    
    def get_daily_trend(self, days=7):
        query = f"""
        SELECT 
            DATE(timestamp) as date,
            COUNT(*) as user_count,
            SUM(CASE WHEN status LIKE 'Uygun%%' THEN 1 ELSE 0 END) as eligible_users,
            SUM(CASE WHEN message_log = 'Mesaj Gönderildi' THEN 1 ELSE 0 END) as messages_sent,
            AVG(viewer_count) as avg_viewers
        FROM live_data
        WHERE timestamp >= DATE_SUB(CURDATE(), INTERVAL %s DAY)
        GROUP BY DATE(timestamp)
        ORDER BY date DESC
        """
        
        return self.execute_query(query, (days,)) or []
       
    
    def execute_query_cached(self, query: str, params: tuple = None, cache_key: str = None, cache_time: int = 60):
        """
        Sorguları önbellekli (cached) çalıştıran bir metod.
        Sık kullanılan ve değişmeyen sorgular için performans kazandırır.
        
        Args:
            query: Çalıştırılacak SQL sorgusu
            params: Sorgu parametreleri
            cache_key: Önbellek anahtarı (None ise sorgu+params kullanılır)
            cache_time: Önbellek süresi (saniye)
        """
        if not hasattr(self, '_query_cache'):
            self._query_cache = {}
            self._query_cache_times = {}
        
        # Önbellek anahtarı oluştur
        if not cache_key:
            import hashlib
            cache_key = hashlib.md5((query + str(params)).encode()).hexdigest()
        
        # Cache süresi kontrol et
        import time
        current_time = time.time()
        if (cache_key in self._query_cache and 
            cache_key in self._query_cache_times and 
            current_time - self._query_cache_times[cache_key] < cache_time):
            return self._query_cache[cache_key]
        
        # Cache'de yoksa veya süresi dolmuşsa yeniden çalıştır
        result = self.execute_query(query, params)
        
        # Sonucu cache'e ekle
        self._query_cache[cache_key] = result
        self._query_cache_times[cache_key] = current_time
        
        return result

    # Bağlantı havuzu (connection pooling) ekleyin
    def _init_connection_pool(self, pool_size=5):
        """Bağlantı havuzu oluşturur. İlk başlangıçta çağrılır."""
        if not hasattr(self, '_connection_pool'):
            self._connection_pool = []
            
        # Pool'u doldur
        while len(self._connection_pool) < pool_size:
            try:
                conn = pymysql.connect(
                    host=self.host,
                    user=self.user,
                    password=self.password,
                    database=self.database,
                    cursorclass=pymysql.cursors.DictCursor,
                    charset='utf8mb4',
                    autocommit=True
                )
                self._connection_pool.append(conn)
            except Exception as e:
                logger.error(f"Bağlantı havuzu oluşturma hatası: {e}")
                break

    def _get_connection_from_pool(self):
        """Bağlantı havuzundan bir bağlantı alır veya yeni oluşturur."""
        if not hasattr(self, '_connection_pool'):
            self._init_connection_pool()
            
        if not self._connection_pool:
            # Havuz boş ise yeni bağlantı oluştur
            try:
                conn = pymysql.connect(
                    host=self.host,
                    user=self.user,
                    password=self.password,
                    database=self.database,
                    cursorclass=pymysql.cursors.DictCursor,
                    charset='utf8mb4',
                    autocommit=True
                )
                return conn
            except Exception as e:
                logger.error(f"Bağlantı oluşturma hatası: {e}")
                return None
        else:
            # Havuzdan bağlantı al
            return self._connection_pool.pop()

    def _return_connection_to_pool(self, conn):
        """Kullanılan bağlantıyı havuza geri döndürür."""
        if not hasattr(self, '_connection_pool'):
            self._init_connection_pool()
            
        if conn and conn.open:
            try:
                # Bağlantıyı temizle ve havuza geri ekle
                conn.ping(reconnect=True)
                self._connection_pool.append(conn)
            except:
                # Bağlantı sorunlu ise kapat
                try:
                    conn.close()
                except:
                    pass
                
    def get_status_distribution(self, period="all"):
        """
        Durum dağılımı istatistiklerini getirir.
        
        Args:
            period: Zaman periyodu ("today", "week", "month", "all")
            
        Returns:
            Durum ve sayı içeren liste.
        """
        where_clause = ""
        params = ()
        
        if period == "today":
            where_clause = "WHERE DATE(timestamp) = CURDATE()"
        elif period == "week":
            where_clause = "WHERE timestamp >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)"
        elif period == "month":
            where_clause = "WHERE timestamp >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)"
        
        query = f"""
        SELECT 
            COALESCE(status, 'Bilinmiyor') as status, 
            COUNT(*) as count
        FROM {self.table_name}
        {where_clause}
        GROUP BY status
        ORDER BY count DESC
        """
        
        return self.execute_query(query, params) or []
    
    def get_activity_by_hour(self, days=7):
        """
        Saat bazında aktivite dağılımını getirir.
        
        Args:
            days: Kaç gün geriye gidileceği
            
        Returns:
            Saat ve kullanıcı sayısı içeren liste.
        """
        query = """
        SELECT 
            HOUR(timestamp) as hour,
            COUNT(*) as user_count,
            AVG(viewer_count) as avg_viewers
        FROM live_data
        WHERE timestamp >= DATE_SUB(CURDATE(), INTERVAL %s DAY)
        GROUP BY HOUR(timestamp)
        ORDER BY hour
        """
        
        return self.execute_query(query, (days,)) or []
    
    def backup_database(self, backup_file):
        """
        Veritabanının yedeğini alır.
        
        Args:
            backup_file: Yedek dosyasının tam yolu
            
        Returns:
            İşlemin başarılı olup olmadığı
        """
        try:
            with self.get_connection() as conn:
                if not conn:
                    logger.error("Yedekleme için veritabanı bağlantısı kurulamadı.")
                    return False
                
                # Tüm tabloyu çek
                data = self.get_all_live_data()
                if not data:
                    logger.error("Yedeklenecek veri bulunamadı.")
                    return False
                
                # JSON olarak kaydet
                import json
                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                
                logger.info(f"Veritabanı yedeği başarıyla oluşturuldu: {backup_file}")
                return True
        except Exception as e:
            logger.error(f"Veritabanı yedekleme hatası: {e}")
            return False
    
    def restore_from_backup(self, backup_file):
        """
        Yedekten geri yükleme yapar.
        
        Args:
            backup_file: Yedek dosyasının tam yolu
            
        Returns:
            Geri yüklenen kayıt sayısı
        """
        try:
            with self.get_connection() as conn:
                if not conn:
                    logger.error("Geri yükleme için veritabanı bağlantısı kurulamadı.")
                    return 0
                
                # JSON verilerini oku
                import json
                with open(backup_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if not data:
                    logger.error("Yedek dosyasında veri bulunamadı.")
                    return 0
                
                # Geri yükleme işlemi
                restored_count = 0
                for record in data:
                    query = f"""
                    INSERT INTO {self.table_name} (
                        username, viewer_count, status, link, 
                        timestamp, message_log, assigned_to, sorgu_tarihi
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s
                    ) ON DUPLICATE KEY UPDATE
                        username = VALUES(username)
                    """
                    
                    params = (
                        record.get('username', ''),
                        record.get('viewer_count', 0),
                        record.get('status', 'Bekleniyor'),
                        record.get('link', ''),
                        record.get('timestamp', None),
                        record.get('message_log', None),
                        record.get('assigned_to', None),
                        record.get('sorgu_tarihi', None)
                    )
                    
                    result = self.execute_query(query, params)
                    if result is not None:
                        restored_count += 1
                
                logger.info(f"Veritabanı geri yükleme tamamlandı: {restored_count} kayıt")
                return restored_count
        except Exception as e:
            logger.error(f"Veritabanı geri yükleme hatası: {e}")
            return 0

    def check_database_integrity(self):
        """
        Veritabanı bütünlüğünü kontrol eder ve sorunları rapor eder.
        
        Returns:
            (sonuç, hata_listesi) şeklinde bir tuple
        """
        errors = []
        try:
            with self.get_connection() as conn:
                if not conn:
                    errors.append("Veritabanı bağlantısı kurulamadı")
                    return False, errors
                
                # Tablo varlığını kontrol et
                with conn.cursor() as cursor:
                    cursor.execute(f"SHOW TABLES LIKE '{self.table_name}'")
                    if not cursor.fetchone():
                        errors.append(f"'{self.table_name}' tablosu bulunamadı")
                        return False, errors
                
                # Sütun yapısını kontrol et
                required_columns = {
                    'id': 'INT', 
                    'username': 'VARCHAR', 
                    'viewer_count': 'DOUBLE',
                    'status': 'VARCHAR', 
                    'link': 'VARCHAR', 
                    'timestamp': 'DATETIME'
                }
                
                with conn.cursor() as cursor:
                    cursor.execute(f"DESCRIBE {self.table_name}")
                    columns = {row['Field']: row['Type'] for row in cursor.fetchall()}
                
                for col, col_type in required_columns.items():
                    if col not in columns:
                        errors.append(f"Gerekli sütun eksik: {col}")
                    elif col == "viewer_count":
                        # viewer_count için özel kontrol - hem INT hem DOUBLE kabul edilebilir
                        if not ("INT" in columns[col].upper() or "DOUBLE" in columns[col].upper() or "DECIMAL" in columns[col].upper() or "FLOAT" in columns[col].upper()):
                            errors.append(f"Sütun tipi uyumsuz: {col} ({columns[col]} - beklenen: INT veya DOUBLE)")
                    elif col_type.upper() not in columns[col].upper():
                        errors.append(f"Sütun tipi uyumsuz: {col} ({columns[col]} - beklenen: {col_type})")
                
                # İndeksleri kontrol et
                with conn.cursor() as cursor:
                    cursor.execute(f"SHOW INDEX FROM {self.table_name}")
                    indexes = [row['Key_name'] for row in cursor.fetchall()]
                
                if 'PRIMARY' not in indexes:
                    errors.append("Primary key indeksi bulunamadı")
                
                # Örnek kayıt sorgulama
                try:
                    test_query = f"SELECT COUNT(*) as cnt FROM {self.table_name} LIMIT 1"
                    self.execute_query(test_query)
                except Exception as e:
                    errors.append(f"Test sorgusu başarısız: {e}")
            
            return len(errors) == 0, errors
        
        except Exception as e:
            errors.append(f"Bütünlük kontrolü hatası: {e}")
            return False, errors

    def optimize_table(self):
        """
        Tablo optimizasyonu ve bakımı yapar.
        
        Returns:
            İşlemin başarılı olup olmadığı
        """
        try:
            with self.get_connection() as conn:
                if not conn:
                    logger.error("Optimizasyon için veritabanı bağlantısı kurulamadı.")
                    return False
                
                with conn.cursor() as cursor:
                    # Tabloyu optimize et
                    cursor.execute(f"OPTIMIZE TABLE {self.table_name}")
                    
                    # Tabloyu analiz et
                    cursor.execute(f"ANALYZE TABLE {self.table_name}")
                    
                logger.info(f"Tablo optimizasyonu başarıyla tamamlandı: {self.table_name}")
                return True
        except Exception as e:
            logger.error(f"Tablo optimizasyonu hatası: {e}")
            return False
            
    def execute_query(self, query: str, params: tuple = None, many: bool = False):
        """
        Executes a given SQL query with optional parameters.

        Args:
            query: The SQL query to execute.
            params: A tuple of parameters for the query.
            many: If True, uses executemany for bulk operations.

        Returns:
            For SELECT queries, returns a list of rows (dictionaries).
            For other queries, returns None.
        """
        try:
            with self.get_connection() as conn:
                if not conn:
                    logger.error("No connection available for query execution.")
                    return None
                    
                with conn.cursor() as cursor:
                    try:
                        if many and params:
                            cursor.executemany(query, params)
                        else:
                            # Eğer params None ise boş tuple kullan
                            safe_params = params if params is not None else ()
                            cursor.execute(query, safe_params)
                            
                        if query.strip().lower().startswith("select"):
                            result = cursor.fetchall() or []
                            logger.debug(f"Query executed: {query} with params: {params}. Fetched {len(result)} rows.")
                            return result
                        else:
                            conn.commit()
                            logger.debug(f"Non-select query executed: {query} with params: {params}.")
                            return None
                    except Exception as e:
                        logger.error(f"Cursor execution error: {e}. Query: {query}")
                        return None
        except Exception as e:
            logger.error(f"Query execution error: {e}. Query: {query}")
            return None

    def get_candidates_with_status(self, status: str = "Bekleniyor", limit: int = 30):
        """
        Retrieves a list of usernames with a specific status.

        Args:
            status: The status to filter by (default "Bekleniyor").
            limit: Maximum number of results to return (default 30).

        Returns:
            A list of usernames.
        """
        query = f"""
            SELECT username
            FROM {self.table_name}
            WHERE status = %s
            LIMIT %s
        """
        rows = self.execute_query(query, (status, limit))
        return [row["username"] for row in rows] if rows else []

    def get_candidates_for_general_sorgu(self, last_id: int, limit: int = 30):
        """
        Retrieves users with an id greater than last_id, ordered ascendingly.

        Args:
            last_id: The last processed user id.
            limit: Maximum number of users to retrieve.

        Returns:
            A list of dictionaries with 'id' and 'username' keys.
        """
        query = f"""
            SELECT id, username
            FROM {self.table_name}
            WHERE id > %s
            ORDER BY id ASC
            LIMIT %s
        """
        return self.execute_query(query, (last_id, limit)) or []

    def get_candidates_all(self, limit: int = 30):
        """
        Retrieves all usernames in ascending order of their id.

        Args:
            limit: Maximum number of usernames to retrieve.

        Returns:
            A list of usernames.
        """
        query = f"SELECT username FROM {self.table_name} ORDER BY id ASC LIMIT %s"
        rows = self.execute_query(query, (limit,))
        return [row["username"] for row in rows] if rows else []

    def get_all_live_data(self):
        """
        Retrieves all rows from the live_data table.

        Returns:
            A list of dictionaries representing the table rows.
        """
        return self.execute_query(f"SELECT * FROM {self.table_name};")

    def get_user_count(self) -> int:
        """
        Retrieves the total number of users.

        Returns:
            The user count as an integer.
        """
        query = f"SELECT COUNT(*) as cnt FROM {self.table_name}"
        result = self.execute_query(query)
        if result and "cnt" in result[0]:
            return result[0]['cnt']
        return 0

    def save_user(self, username: str, viewer_count: int, link: str):
        """
        Inserts a new user into the database or updates an existing user's record.
        Updates viewer_count, link, and timestamp if the user already exists.

        Args:
            username: The username.
            viewer_count: The current viewer count.
            link: The URL/link associated with the user.

        Returns:
            A tuple (lastrowid, is_updated) where is_updated is True if the record was updated.
        """
        query = f"""
        INSERT INTO {self.table_name} (username, viewer_count, link, timestamp, status)
        VALUES (%s, %s, %s, NOW(), 'Bekleniyor')
        ON DUPLICATE KEY UPDATE
            viewer_count = VALUES(viewer_count),
            link = VALUES(link),
            status = 'Bekleniyor',
            timestamp = NOW()
        """
        try:
            with self.get_connection() as conn:
                if not conn:
                    logger.error("Connection not available for saving user.")
                    return None, False
                with conn.cursor() as cursor:
                    cursor.execute(query, (username, viewer_count, link))
                    conn.commit()
                    return cursor.lastrowid, cursor.rowcount > 1
        except Exception as e:
            logger.error(f"Error in save_user: {e}")
            return None, False

    def update_status_bulk(self, updates):
        """
        Performs a bulk update of user statuses.

        Args:
            updates: A list of tuples in the form (status, sorgu_tarihi, username).
        """
        query = f"""
        UPDATE {self.table_name}
           SET status = %s,
               sorgu_tarihi = %s
         WHERE username = %s
        """
        try:
            with self.get_connection() as conn:
                if not conn:
                    logger.error("Connection not available for bulk update.")
                    return
                with conn.cursor() as cursor:
                    cursor.executemany(query, updates)
                conn.commit()
                logger.info(f"Bulk status update executed for {len(updates)} records.")
        except Exception as e:
            logger.error(f"Error in update_status_bulk: {e}")

    def load_unassigned_publishers(self):
        """
        Retrieves publishers that have not been assigned.

        Returns:
            A list of dictionaries containing publisher details.
        """
        query = f"""
            SELECT l.id,
                   l.username,
                   l.viewer_count,
                   l.status,
                   l.link,
                   l.timestamp,
                   s.name AS scout_name
              FROM {self.table_name} l
         LEFT JOIN scouts s ON l.assigned_to = s.id
             WHERE l.assigned_to IS NULL
          ORDER BY l.id ASC
        """
        return self.execute_query(query)

    def get_recently_added_publishers(self, since_datetime):
        """
        Retrieves publishers added after the given datetime.

        Args:
            since_datetime: The datetime to filter new publishers.

        Returns:
            A list of dictionaries representing the new publishers.
        """
        query = f"""
            SELECT *
              FROM {self.table_name}
             WHERE timestamp >= %s
        """
        return self.execute_query(query, (since_datetime,))

    def update_communication_status(self, publisher_id, status: str, result: str, notes: str):
        """
        Updates the communication status for a specific publisher.
        Records the status, result, and any additional notes in the message_log column.

        Args:
            publisher_id: The ID of the publisher.
            status: The status string.
            result: The result string.
            notes: Additional notes regarding the communication.
        """
        query = f"UPDATE {self.table_name} SET message_log=%s WHERE id=%s"
        combined = f"{status}/{result} - {notes}"
        self.execute_query(query, (combined, publisher_id))

    def delete_performance(self, performance_id):
        """
        Placeholder for a delete operation.
        Customize this method based on project requirements.

        Args:
            performance_id: The ID of the performance record to delete.
        """
        pass

    def close(self):
        """Tüm bağlantı havuzundaki bağlantıları kapatır."""
        if hasattr(self, '_connection_pool'):
            for conn in self._connection_pool:
                try:
                    conn.close()
                except Exception:
                    pass
            self._connection_pool.clear()
