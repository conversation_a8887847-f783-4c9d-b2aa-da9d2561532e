#!/bin/bash

# TikTok Automation Linux Başlatma Script'i
# AlmaLinux 9 için optimize edilmiş

# Script'in bulunduğu dizine git
cd "$(dirname "$0")"

echo "🚀 TikTok Automation başlatılıyor..."
echo "📁 Çalışma dizini: $(pwd)"

# Python3 kurulu mu kontrol et
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 bulunamadı. Lütfen Python3'ü kurun:"
    echo "sudo dnf install python3 python3-pip -y"
    exit 1
fi

# ChromeDriver kurulu mu kontrol et
if ! command -v chromedriver &> /dev/null; then
    echo "❌ ChromeDriver bulunamadı. Lütfen ChromeDriver'ı kurun:"
    echo "sudo dnf install chromedriver -y"
    exit 1
fi

# Chromium kurulu mu kontrol et
if ! command -v chromium-browser &> /dev/null; then
    echo "❌ Chromium bulunamadı. Lütfen Chromium'u kurun:"
    echo "sudo dnf install chromium chromium-headless -y"
    exit 1
fi

# Virtual environment oluştur (eğer yoksa)
if [ ! -d "venv" ]; then
    echo "📦 Virtual environment oluşturuluyor..."
    python3 -m venv venv
fi

# Virtual environment'ı aktifleştir
echo "🔧 Virtual environment aktifleştiriliyor..."
source venv/bin/activate

# Bağımlılıkları kur
if [ -f "requirements.txt" ]; then
    echo "📥 Bağımlılıklar kuruluyor..."
    pip install -r requirements.txt
else
    echo "⚠️ requirements.txt bulunamadı, temel bağımlılıklar kuruluyor..."
    pip install selenium pymysql python-dotenv openpyxl requests
fi

# .env dosyası var mı kontrol et
if [ ! -f ".env" ]; then
    echo "⚠️ .env dosyası bulunamadı. Örnek .env dosyası oluşturuluyor..."
    cat > .env << EOF
# Veritabanı Ayarları
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=tiktok_live_data

# MySQL Ayarları (alternatif)
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_PASSWORD=
MYSQL_DATABASE=tiktok_live_data
EOF
    echo "📝 .env dosyası oluşturuldu. Lütfen veritabanı ayarlarını düzenleyin."
fi

# Automation worker'ı başlat
echo "🎯 Automation worker başlatılıyor..."
python3 automation_worker.py

echo "✅ Script tamamlandı."
