2025-08-05 01:36:11,982 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-08-05 01:36:11,982 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\desktop\Tuber X-Akademi\Yayıncı Avı <PERSON>ü<PERSON> (automation_worker.py:253)
2025-08-05 01:36:11,998 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-08-05 01:36:12,014 [INFO] __main__ - ✅ params alanı LONGTEXT'e g<PERSON> (automation_worker.py:502) (automation_worker.py:185)
2025-08-05 01:36:12,014 [INFO] __main__ - ✅ Siste<PERSON> hazır - komutlar bekle<PERSON>yor (automation_worker.py:198)
2025-08-05 01:36:30,092 [INFO] __main__ - 📨 Komut: start (ID: 395) (automation_worker.py:214)
2025-08-05 01:36:30,092 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-08-05 01:36:33,264 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-08-05 01:36:33,264 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-08-05 01:36:33,264 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-08-05 01:36:33,264 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-08-05 01:36:33,264 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-08-05 01:36:33,264 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-08-05 01:36:33,264 [INFO] main - 🔧 start_scraper çağrıldı (main.py:325)
2025-08-05 01:36:33,264 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:327)
2025-08-05 01:36:33,264 [INFO] main - 🔧 Phase lock alındı (main.py:330)
2025-08-05 01:36:33,264 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:347)
2025-08-05 01:36:33,264 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:350)
2025-08-05 01:36:33,264 [INFO] main - ✅ Scraper thread başlatıldı (main.py:364)
2025-08-05 01:36:33,264 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 395) (automation_worker.py:150)
2025-08-05 01:36:39,857 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-08-05 01:36:39,857 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-08-05 01:36:51,410 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-08-05 01:36:58,352 [INFO] scraper_thread - [SCRAPER] 7thedante7 | 3000 (scraper_thread.py:321)
2025-08-05 01:37:03,283 [INFO] scraper_thread - [SCRAPER] turco.art | 1 (scraper_thread.py:321)
2025-08-05 01:37:07,385 [INFO] scraper_thread - [SCRAPER] beyzakmen | 4 (scraper_thread.py:321)
2025-08-05 01:37:11,464 [INFO] scraper_thread - [SCRAPER] hayalgul65 | 4 (scraper_thread.py:321)
2025-08-05 01:37:17,593 [INFO] scraper_thread - [SCRAPER] esmerbarbiee2 | 8 (scraper_thread.py:321)
2025-08-05 01:37:21,422 [INFO] scraper_thread - [SCRAPER] mami38045 | 3 (scraper_thread.py:321)
2025-08-05 01:37:25,663 [INFO] scraper_thread - [SCRAPER] ahmet.iste1 | 10 (scraper_thread.py:321)
2025-08-05 01:37:30,188 [INFO] scraper_thread - [SCRAPER] reis_ankara0606 | 1 (scraper_thread.py:321)
2025-08-05 01:37:35,637 [INFO] scraper_thread - [SCRAPER] edaasahhiin | 4 (scraper_thread.py:321)
2025-08-05 01:37:40,597 [INFO] scraper_thread - [SCRAPER] cpm.garage057 | 5 (scraper_thread.py:321)
2025-08-05 01:37:46,100 [INFO] scraper_thread - [SCRAPER] mesut.ayhan | 0 (scraper_thread.py:321)
2025-08-05 01:37:50,557 [INFO] scraper_thread - [SCRAPER] helin.heviii | 110 (scraper_thread.py:321)
2025-08-05 01:37:54,516 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 12 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-08-05 01:37:57,092 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-08-05 01:37:57,092 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:225)
2025-08-05 01:37:57,093 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:228)
2025-08-05 01:37:57,093 [INFO] main - ✅ Scraper thread temizlendi (callback) (main.py:231)
2025-08-05 01:37:57,093 [INFO] main - 🔍 Son 1 dakikada toplam 12 kullanıcı: (main.py:248)
2025-08-05 01:37:57,093 [INFO] main -   - helin.heviii: Bekleniyor (2025-08-05 01:37:50) (main.py:250)
2025-08-05 01:37:57,093 [INFO] main -   - mesut.ayhan: Bekleniyor (2025-08-05 01:37:46) (main.py:250)
2025-08-05 01:37:57,093 [INFO] main -   - cpm.garage057: Bekleniyor (2025-08-05 01:37:40) (main.py:250)
2025-08-05 01:37:57,093 [INFO] main -   - edaasahhiin: Bekleniyor (2025-08-05 01:37:35) (main.py:250)
2025-08-05 01:37:57,093 [INFO] main -   - reis_ankara0606: Bekleniyor (2025-08-05 01:37:30) (main.py:250)
2025-08-05 01:37:57,093 [INFO] main -   - ahmet.iste1: Bekleniyor (2025-08-05 01:37:25) (main.py:250)
2025-08-05 01:37:57,093 [INFO] main -   - mami38045: Bekleniyor (2025-08-05 01:37:21) (main.py:250)
2025-08-05 01:37:57,093 [INFO] main -   - esmerbarbiee2: Bekleniyor (2025-08-05 01:37:17) (main.py:250)
2025-08-05 01:37:57,093 [INFO] main -   - hayalgul65: Bekleniyor (2025-08-05 01:37:11) (main.py:250)
2025-08-05 01:37:57,093 [INFO] main -   - beyzakmen: Bekleniyor (2025-08-05 01:37:07) (main.py:250)
2025-08-05 01:37:57,093 [INFO] main -   - turco.art: Bekleniyor (2025-08-05 01:37:03) (main.py:250)
2025-08-05 01:37:57,093 [INFO] main -   - 7thedante7: Bekleniyor (2025-08-05 01:36:58) (main.py:250)
2025-08-05 01:37:57,093 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:254)
2025-08-05 01:37:57,093 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:373)
2025-08-05 01:37:57,093 [INFO] main - 🔧 Phase lock alınıyor... (main.py:379)
2025-08-05 01:37:57,093 [INFO] main - 🔧 Phase lock alındı (main.py:381)
2025-08-05 01:37:57,093 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:389)
2025-08-05 01:37:57,093 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:392)
2025-08-05 01:37:57,093 [INFO] main - 📋 Kullanıcılar: ['7thedante7', 'turco.art', 'beyzakmen', 'hayalgul65', 'esmerbarbiee2', 'mami38045', 'ahmet.iste1', 'reis_ankara0606', 'edaasahhiin', 'cpm.garage057', 'mesut.ayhan', 'helin.heviii'] (main.py:393)
2025-08-05 01:37:57,093 [INFO] main - 📋 Publishers formatı: [{'username': '7thedante7'}, {'username': 'turco.art'}, {'username': 'beyzakmen'}, {'username': 'hayalgul65'}, {'username': 'esmerbarbiee2'}, {'username': 'mami38045'}, {'username': 'ahmet.iste1'}, {'username': 'reis_ankara0606'}, {'username': 'edaasahhiin'}, {'username': 'cpm.garage057'}, {'username': 'mesut.ayhan'}, {'username': 'helin.heviii'}] (main.py:397)
2025-08-05 01:37:57,093 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:399)
2025-08-05 01:37:57,093 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:409)
2025-08-05 01:37:57,093 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:410)
2025-08-05 01:37:57,093 [INFO] main - 🔧 Thread daemon: True (main.py:411)
2025-08-05 01:37:57,093 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:420)
2025-08-05 01:37:57,108 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-08-05 01:37:57,108 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:424)
2025-08-05 01:37:57,108 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 12 (status_checker.py:84)
2025-08-05 01:37:57,108 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 9956 (status_checker.py:84)
2025-08-05 01:37:57,108 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:433)
2025-08-05 01:37:57,108 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-08-05 01:37:57,108 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-08-05 01:37:57,108 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-08-05 01:37:57,108 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-08-05 01:38:00,045 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-08-05 01:38:00,279 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-08-05 01:38:00,279 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı işlenecek (status_checker.py:84)
2025-08-05 01:38:00,279 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': '7thedante7'}, {'username': 'turco.art'}, {'username': 'beyzakmen'}] (status_checker.py:84)
2025-08-05 01:38:00,279 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-08-05 01:38:00,279 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-08-05 01:38:00,279 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-08-05 01:38:00,279 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-08-05 01:38:00,279 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-08-05 01:38:00,279 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-08-05 01:38:00,279 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-08-05 01:38:00,279 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-08-05 01:38:01,749 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 01:38:04,764 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-08-05 01:38:04,764 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 01:38:04,764 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-08-05 01:38:04,764 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-08-05 01:38:12,108 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-08-05 01:38:12,139 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-08-05 01:38:12,752 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-08-05 01:38:13,458 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-08-05 01:38:13,489 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-08-05 01:38:13,490 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-08-05 01:38:16,822 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-08-05 01:38:16,822 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 12 kullanıcı). (status_checker.py:84)
2025-08-05 01:38:20,107 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-08-05 01:38:25,444 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-08-05 01:38:28,068 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-08-05 01:38:30,244 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-08-05 01:38:30,311 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 12 (status_checker.py:84)
2025-08-05 01:38:30,316 [INFO] StatusChecker - [STATUS_CHECKER] 💾 12 kaydın durumu güncellendi. (status_checker.py:84)
2025-08-05 01:38:33,749 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-08-05 01:38:38,373 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-08-05 01:38:38,498 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-08-05 01:38:38,498 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-08-05 01:38:38,498 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:271)
2025-08-05 01:38:38,498 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:274)
2025-08-05 01:38:38,514 [INFO] main - ✅ Status Checker thread temizlendi (callback) (main.py:277)
2025-08-05 01:38:38,514 [INFO] main - 3️⃣ Message Sender başlatılıyor (9 kullanıcı) (main.py:289)
2025-08-05 01:38:38,514 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:454)
2025-08-05 01:38:38,514 [INFO] main - ✅ Thread temizlendi, scraper başlatılıyor (main.py:458)
2025-08-05 01:38:39,529 [INFO] main - 🔧 start_scraper çağrıldı (main.py:325)
2025-08-05 01:38:39,529 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:327)
2025-08-05 01:38:39,530 [INFO] main - 🔧 Phase lock alındı (main.py:330)
2025-08-05 01:38:39,530 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:347)
2025-08-05 01:38:39,530 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:350)
2025-08-05 01:38:39,530 [INFO] main - ✅ Scraper thread başlatıldı (main.py:364)
2025-08-05 01:38:43,575 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-08-05 01:38:43,575 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-08-05 01:38:51,337 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-08-05 01:38:56,573 [INFO] scraper_thread - [SCRAPER] 7thedante7 | 3400 (scraper_thread.py:321)
2025-08-05 01:39:00,439 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-08-05 01:39:03,848 [INFO] scraper_thread - [SCRAPER] hclik45 | 0 (scraper_thread.py:321)
2025-08-05 01:39:09,117 [INFO] scraper_thread - [SCRAPER] karaca.batu16 | 0 (scraper_thread.py:321)
2025-08-05 01:39:14,775 [INFO] scraper_thread - [SCRAPER] scholesback7 | 4 (scraper_thread.py:321)
2025-08-05 01:39:19,156 [INFO] scraper_thread - [SCRAPER] enversrknn | 10 (scraper_thread.py:321)
2025-08-05 01:39:24,049 [INFO] scraper_thread - [SCRAPER] isi.0071 | 2 (scraper_thread.py:321)
2025-08-05 01:39:30,583 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-08-05 01:39:34,293 [INFO] scraper_thread - [SCRAPER] bedirhan..1998 | 0 (scraper_thread.py:321)
2025-08-05 01:39:39,335 [INFO] scraper_thread - [SCRAPER] user6162101085413 | 0 (scraper_thread.py:321)
2025-08-05 01:39:44,275 [INFO] scraper_thread - [SCRAPER] denniizzz1 | 3 (scraper_thread.py:321)
2025-08-05 01:39:49,816 [INFO] scraper_thread - [SCRAPER] kadir.basara01 | 9 (scraper_thread.py:321)
2025-08-05 01:39:54,662 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 10 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-08-05 01:39:57,248 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-08-05 01:39:57,248 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:225)
2025-08-05 01:39:57,249 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:228)
2025-08-05 01:39:57,249 [INFO] main - ✅ Scraper thread temizlendi (callback) (main.py:231)
2025-08-05 01:39:57,249 [INFO] main - 🔍 Son 1 dakikada toplam 9 kullanıcı: (main.py:248)
2025-08-05 01:39:57,249 [INFO] main -   - kadir.basara01: Bekleniyor (2025-08-05 01:39:49) (main.py:250)
2025-08-05 01:39:57,249 [INFO] main -   - denniizzz1: Bekleniyor (2025-08-05 01:39:44) (main.py:250)
2025-08-05 01:39:57,249 [INFO] main -   - user6162101085413: Bekleniyor (2025-08-05 01:39:39) (main.py:250)
2025-08-05 01:39:57,249 [INFO] main -   - bedirhan..1998: Bekleniyor (2025-08-05 01:39:34) (main.py:250)
2025-08-05 01:39:57,249 [INFO] main -   - isi.0071: Bekleniyor (2025-08-05 01:39:24) (main.py:250)
2025-08-05 01:39:57,249 [INFO] main -   - enversrknn: Bekleniyor (2025-08-05 01:39:19) (main.py:250)
2025-08-05 01:39:57,249 [INFO] main -   - scholesback7: Bekleniyor (2025-08-05 01:39:14) (main.py:250)
2025-08-05 01:39:57,249 [INFO] main -   - karaca.batu16: Bekleniyor (2025-08-05 01:39:09) (main.py:250)
2025-08-05 01:39:57,249 [INFO] main -   - hclik45: Bekleniyor (2025-08-05 01:39:03) (main.py:250)
2025-08-05 01:39:57,249 [INFO] main - 2️⃣ Status Checker başlatılıyor (9 kullanıcı) (main.py:254)
2025-08-05 01:39:57,249 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:373)
2025-08-05 01:39:57,249 [INFO] main - 🔧 Phase lock alınıyor... (main.py:379)
2025-08-05 01:39:57,249 [INFO] main - 🔧 Phase lock alındı (main.py:381)
2025-08-05 01:39:57,249 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:389)
2025-08-05 01:39:57,249 [INFO] main - 2️⃣ Status Checker başlatılıyor (9 kullanıcı) (main.py:392)
2025-08-05 01:39:57,249 [INFO] main - 📋 Kullanıcılar: ['hclik45', 'karaca.batu16', 'scholesback7', 'enversrknn', 'isi.0071', 'bedirhan..1998', 'user6162101085413', 'denniizzz1', 'kadir.basara01'] (main.py:393)
2025-08-05 01:39:57,249 [INFO] main - 📋 Publishers formatı: [{'username': 'hclik45'}, {'username': 'karaca.batu16'}, {'username': 'scholesback7'}, {'username': 'enversrknn'}, {'username': 'isi.0071'}, {'username': 'bedirhan..1998'}, {'username': 'user6162101085413'}, {'username': 'denniizzz1'}, {'username': 'kadir.basara01'}] (main.py:397)
2025-08-05 01:39:57,249 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:399)
2025-08-05 01:39:57,249 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:409)
2025-08-05 01:39:57,249 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:410)
2025-08-05 01:39:57,249 [INFO] main - 🔧 Thread daemon: True (main.py:411)
2025-08-05 01:39:57,249 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:420)
2025-08-05 01:39:57,249 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-08-05 01:39:57,249 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:424)
2025-08-05 01:39:57,249 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 9 (status_checker.py:84)
2025-08-05 01:39:57,249 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:433)
2025-08-05 01:39:57,249 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 6052 (status_checker.py:84)
2025-08-05 01:39:57,249 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-08-05 01:39:57,249 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-08-05 01:39:57,249 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-08-05 01:39:57,249 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-08-05 01:40:00,404 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-08-05 01:40:00,404 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 9 kullanıcı işlenecek (status_checker.py:84)
2025-08-05 01:40:00,405 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'hclik45'}, {'username': 'karaca.batu16'}, {'username': 'scholesback7'}] (status_checker.py:84)
2025-08-05 01:40:00,405 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-08-05 01:40:00,405 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-08-05 01:40:00,405 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-08-05 01:40:00,405 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-08-05 01:40:00,405 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-08-05 01:40:00,405 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-08-05 01:40:00,405 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-08-05 01:40:00,405 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-08-05 01:40:00,701 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-08-05 01:40:02,717 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 01:40:05,748 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-08-05 01:40:05,748 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 01:40:05,748 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-08-05 01:40:05,764 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-08-05 01:40:11,569 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-08-05 01:40:11,579 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-08-05 01:40:11,722 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-08-05 01:40:11,949 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-08-05 01:40:11,950 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-08-05 01:40:11,951 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-08-05 01:40:15,251 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-08-05 01:40:15,251 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 9 kullanıcı). (status_checker.py:84)
2025-08-05 01:40:19,311 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-08-05 01:40:24,601 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 9 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-08-05 01:40:27,345 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-08-05 01:40:28,997 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 9 (status_checker.py:84)
2025-08-05 01:40:29,002 [INFO] StatusChecker - [STATUS_CHECKER] 💾 9 kaydın durumu güncellendi. (status_checker.py:84)
2025-08-05 01:40:30,936 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-08-05 01:40:32,359 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-08-05 01:40:36,842 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-08-05 01:40:36,967 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-08-05 01:40:36,982 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-08-05 01:40:36,982 [INFO] main - 🏁 status_checker tamamlandı (finished flag) (main.py:134)
2025-08-05 01:40:36,982 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:271)
2025-08-05 01:40:36,982 [INFO] main - 🔧 Thread temizleniyor: status_checker (main.py:144)
2025-08-05 01:40:36,982 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:274)
2025-08-05 01:40:36,982 [INFO] main - ✅ Thread temizlendi (main.py:147)
2025-08-05 01:40:36,982 [INFO] main - ✅ Status Checker thread temizlendi (callback) (main.py:277)
2025-08-05 01:40:36,982 [INFO] main - 3️⃣ Message Sender başlatılıyor (8 kullanıcı) (main.py:289)
2025-08-05 01:40:36,982 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:454)
2025-08-05 01:40:36,982 [INFO] main - ✅ Thread temizlendi, scraper başlatılıyor (main.py:458)
2025-08-05 01:40:37,998 [INFO] main - 🔧 start_scraper çağrıldı (main.py:325)
2025-08-05 01:40:37,999 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:327)
2025-08-05 01:40:38,998 [INFO] main - 🔄 status_checker sonrası işlem başlatılıyor... (main.py:156)
2025-08-05 01:40:38,998 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:271)
2025-08-05 01:40:38,999 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:274)
2025-08-05 01:40:38,999 [INFO] main - ✅ Status Checker thread temizlendi (callback) (main.py:277)
2025-08-05 01:40:38,999 [INFO] main - 3️⃣ Message Sender başlatılıyor (8 kullanıcı) (main.py:289)
2025-08-05 01:40:38,999 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:454)
2025-08-05 01:40:38,999 [INFO] main - ✅ Thread temizlendi, scraper başlatılıyor (main.py:458)
2025-08-05 01:40:40,014 [INFO] main - 🔧 start_scraper çağrıldı (main.py:325)
2025-08-05 01:40:40,014 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:327)
2025-08-05 15:17:25,217 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-08-05 15:17:25,217 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-08-05 15:17:25,217 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:207)
2025-08-05 15:17:25,217 [INFO] main - ✅ Otomasyon durduruldu (main.py:216)
2025-08-05 15:17:25,404 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-08-05 15:17:29,576 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-08-05 15:17:29,576 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-08-05 15:17:29,576 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-08-05 15:17:29,592 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-08-05 15:17:29,592 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-08-05 15:17:37,639 [INFO] __main__ - 📨 Komut: start (ID: 396) (automation_worker.py:214)
2025-08-05 15:17:37,639 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-08-05 15:17:40,779 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-08-05 19:40:22,201 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-08-05 19:40:22,201 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-08-05 19:40:22,217 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-08-05 19:40:22,217 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-08-05 19:40:22,217 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-08-05 19:40:22,217 [INFO] main - 🔧 start_scraper çağrıldı (main.py:325)
2025-08-05 19:40:22,217 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:327)
2025-08-05 19:40:22,217 [INFO] main - 🔧 Phase lock alındı (main.py:330)
2025-08-05 19:40:22,217 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:347)
2025-08-05 19:40:22,217 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:350)
2025-08-05 19:40:22,217 [INFO] main - ✅ Scraper thread başlatıldı (main.py:364)
2025-08-05 19:40:22,232 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 396) (automation_worker.py:150)
2025-08-05 19:40:27,122 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-08-05 19:40:27,122 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-08-05 19:40:30,369 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-08-05 19:40:37,057 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-08-05 19:40:43,977 [INFO] scraper_thread - [SCRAPER] mehmetalierbilofficial | 928 (scraper_thread.py:321)
2025-08-05 19:40:48,570 [INFO] scraper_thread - [SCRAPER] serkan.kesekler | 4 (scraper_thread.py:321)
2025-08-05 19:40:53,492 [INFO] scraper_thread - [SCRAPER] volkanercan27 | 1 (scraper_thread.py:321)
2025-08-05 19:40:57,786 [INFO] scraper_thread - [SCRAPER] esmeradam576 | 9 (scraper_thread.py:321)
2025-08-05 19:41:00,572 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-08-05 19:41:02,098 [INFO] scraper_thread - [SCRAPER] mardinliydk1 | 13 (scraper_thread.py:321)
2025-08-05 19:41:07,354 [INFO] scraper_thread - [SCRAPER] burcyarenn | 7 (scraper_thread.py:321)
2025-08-05 19:41:12,158 [INFO] scraper_thread - [SCRAPER] .aysima78 | 3 (scraper_thread.py:321)
2025-08-05 19:41:17,416 [INFO] scraper_thread - [SCRAPER] hilmi.can.akgn | 6 (scraper_thread.py:321)
2025-08-05 19:41:22,561 [INFO] scraper_thread - [SCRAPER] birdelinindelisi68 | 3 (scraper_thread.py:321)
2025-08-05 19:41:26,283 [INFO] scraper_thread - [SCRAPER] alperenkaanboyraz | 3 (scraper_thread.py:321)
2025-08-05 19:41:30,747 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-08-05 19:41:31,377 [INFO] scraper_thread - [SCRAPER] memocan_cick | 0 (scraper_thread.py:321)
2025-08-05 19:41:36,058 [INFO] scraper_thread - [SCRAPER] ahinkartal63 | 2 (scraper_thread.py:321)
2025-08-05 19:41:40,336 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 12 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-08-05 19:41:42,800 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-08-05 19:41:42,800 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:225)
2025-08-05 19:41:42,800 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:228)
2025-08-05 19:41:42,800 [INFO] main - ✅ Scraper thread temizlendi (callback) (main.py:231)
2025-08-05 19:41:42,800 [INFO] main - 🔍 Son 1 dakikada toplam 12 kullanıcı: (main.py:248)
2025-08-05 19:41:42,800 [INFO] main -   - ahinkartal63: Bekleniyor (2025-08-05 19:41:36) (main.py:250)
2025-08-05 19:41:42,800 [INFO] main -   - memocan_cick: Bekleniyor (2025-08-05 19:41:31) (main.py:250)
2025-08-05 19:41:42,800 [INFO] main -   - alperenkaanboyraz: Bekleniyor (2025-08-05 19:41:26) (main.py:250)
2025-08-05 19:41:42,800 [INFO] main -   - birdelinindelisi68: Bekleniyor (2025-08-05 19:41:22) (main.py:250)
2025-08-05 19:41:42,800 [INFO] main -   - hilmi.can.akgn: Bekleniyor (2025-08-05 19:41:17) (main.py:250)
2025-08-05 19:41:42,800 [INFO] main -   - .aysima78: Bekleniyor (2025-08-05 19:41:12) (main.py:250)
2025-08-05 19:41:42,800 [INFO] main -   - burcyarenn: Bekleniyor (2025-08-05 19:41:07) (main.py:250)
2025-08-05 19:41:42,800 [INFO] main -   - mardinliydk1: Bekleniyor (2025-08-05 19:41:02) (main.py:250)
2025-08-05 19:41:42,800 [INFO] main -   - esmeradam576: Bekleniyor (2025-08-05 19:40:57) (main.py:250)
2025-08-05 19:41:42,800 [INFO] main -   - volkanercan27: Bekleniyor (2025-08-05 19:40:53) (main.py:250)
2025-08-05 19:41:42,800 [INFO] main -   - serkan.kesekler: Bekleniyor (2025-08-05 19:40:48) (main.py:250)
2025-08-05 19:41:42,800 [INFO] main -   - mehmetalierbilofficial: Bekleniyor (2025-08-05 19:40:43) (main.py:250)
2025-08-05 19:41:42,800 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:254)
2025-08-05 19:41:42,800 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:373)
2025-08-05 19:41:42,800 [INFO] main - 🔧 Phase lock alınıyor... (main.py:379)
2025-08-05 19:41:42,800 [INFO] main - 🔧 Phase lock alındı (main.py:381)
2025-08-05 19:41:42,800 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:389)
2025-08-05 19:41:42,800 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:392)
2025-08-05 19:41:42,800 [INFO] main - 📋 Kullanıcılar: ['mehmetalierbilofficial', 'serkan.kesekler', 'volkanercan27', 'esmeradam576', 'mardinliydk1', 'burcyarenn', '.aysima78', 'hilmi.can.akgn', 'birdelinindelisi68', 'alperenkaanboyraz', 'memocan_cick', 'ahinkartal63'] (main.py:393)
2025-08-05 19:41:42,800 [INFO] main - 📋 Publishers formatı: [{'username': 'mehmetalierbilofficial'}, {'username': 'serkan.kesekler'}, {'username': 'volkanercan27'}, {'username': 'esmeradam576'}, {'username': 'mardinliydk1'}, {'username': 'burcyarenn'}, {'username': '.aysima78'}, {'username': 'hilmi.can.akgn'}, {'username': 'birdelinindelisi68'}, {'username': 'alperenkaanboyraz'}, {'username': 'memocan_cick'}, {'username': 'ahinkartal63'}] (main.py:397)
2025-08-05 19:41:42,800 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:399)
2025-08-05 19:41:42,816 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:409)
2025-08-05 19:41:42,816 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:410)
2025-08-05 19:41:42,816 [INFO] main - 🔧 Thread daemon: True (main.py:411)
2025-08-05 19:41:42,816 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:420)
2025-08-05 19:41:42,816 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-08-05 19:41:42,816 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:424)
2025-08-05 19:41:42,816 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 12 (status_checker.py:84)
2025-08-05 19:41:42,816 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:433)
2025-08-05 19:41:42,816 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 3312 (status_checker.py:84)
2025-08-05 19:41:42,816 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-08-05 19:41:42,816 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:41:42,816 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-08-05 19:41:42,816 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-08-05 19:41:46,003 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-08-05 19:41:46,003 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı işlenecek (status_checker.py:84)
2025-08-05 19:41:46,003 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'mehmetalierbilofficial'}, {'username': 'serkan.kesekler'}, {'username': 'volkanercan27'}] (status_checker.py:84)
2025-08-05 19:41:46,003 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-08-05 19:41:46,003 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-08-05 19:41:46,003 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-08-05 19:41:46,003 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-08-05 19:41:46,003 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-08-05 19:41:46,003 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-08-05 19:41:46,003 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-08-05 19:41:46,003 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-08-05 19:41:47,894 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:41:50,914 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-08-05 19:41:50,914 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:41:50,914 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-08-05 19:41:50,914 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-08-05 19:41:59,275 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-08-05 19:41:59,278 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-08-05 19:41:59,304 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-08-05 19:41:59,309 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-08-05 19:41:59,309 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-08-05 19:41:59,310 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-08-05 19:42:00,495 [INFO] StatusChecker - Pop-up kapatıldı (status_checker.py:41)
2025-08-05 19:42:00,495 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-08-05 19:42:00,496 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 12 kullanıcı). (status_checker.py:84)
2025-08-05 19:42:00,929 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-08-05 19:42:03,036 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-08-05 19:42:09,255 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-08-05 19:42:12,286 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-08-05 19:42:14,023 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 12 (status_checker.py:84)
2025-08-05 19:42:14,028 [INFO] StatusChecker - [STATUS_CHECKER] 💾 12 kaydın durumu güncellendi. (status_checker.py:84)
2025-08-05 19:42:17,412 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-08-05 19:42:21,911 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-08-05 19:42:22,051 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-08-05 19:42:22,051 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-08-05 19:42:22,051 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:271)
2025-08-05 19:42:22,051 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:274)
2025-08-05 19:42:22,051 [INFO] main - ✅ Status Checker thread temizlendi (callback) (main.py:277)
2025-08-05 19:42:22,051 [INFO] main - 3️⃣ Message Sender başlatılıyor (7 kullanıcı) (main.py:289)
2025-08-05 19:42:22,051 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:454)
2025-08-05 19:42:22,051 [INFO] main - ✅ Thread temizlendi, scraper başlatılıyor (main.py:458)
2025-08-05 19:42:23,067 [INFO] main - 🔧 start_scraper çağrıldı (main.py:325)
2025-08-05 19:42:23,067 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:327)
2025-08-05 19:42:23,067 [INFO] main - 🔧 Phase lock alındı (main.py:330)
2025-08-05 19:42:23,067 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:347)
2025-08-05 19:42:23,067 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:350)
2025-08-05 19:42:23,067 [INFO] main - ✅ Scraper thread başlatıldı (main.py:364)
2025-08-05 19:42:29,315 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-08-05 19:42:29,316 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-08-05 19:42:35,376 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-08-05 19:42:41,140 [INFO] scraper_thread - [SCRAPER] mehmetalierbilofficial | 1100 (scraper_thread.py:321)
2025-08-05 19:42:46,393 [INFO] scraper_thread - [SCRAPER] mahmmadullahalkoz | 0 (scraper_thread.py:321)
2025-08-05 19:42:51,454 [INFO] scraper_thread - [SCRAPER] aytekinulu | 1 (scraper_thread.py:321)
2025-08-05 19:42:55,844 [INFO] scraper_thread - [SCRAPER] asinehiri | 3 (scraper_thread.py:321)
2025-08-05 19:43:00,287 [INFO] scraper_thread - [SCRAPER] afyonlu.0319 | 5 (scraper_thread.py:321)
2025-08-05 19:43:05,226 [INFO] scraper_thread - [SCRAPER] seiiarsslan | 4 (scraper_thread.py:321)
2025-08-05 19:43:10,603 [INFO] scraper_thread - [SCRAPER] vanessa.pm007 | 69 (scraper_thread.py:321)
2025-08-05 19:43:14,527 [INFO] scraper_thread - [SCRAPER] starfamesfashion | 227 (scraper_thread.py:321)
2025-08-05 19:43:19,502 [INFO] scraper_thread - [SCRAPER] muhammedcetinkaya35 | 0 (scraper_thread.py:321)
2025-08-05 19:43:23,237 [INFO] scraper_thread - [SCRAPER] kumralim_67 | 6 (scraper_thread.py:321)
2025-08-05 19:43:27,973 [INFO] scraper_thread - [SCRAPER] mahsundeniz0445 | 2 (scraper_thread.py:321)
2025-08-05 19:43:32,599 [INFO] scraper_thread - [SCRAPER] legnac.finans | 25 (scraper_thread.py:321)
2025-08-05 19:43:36,388 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 12 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-08-05 19:43:38,814 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-08-05 19:43:38,814 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:225)
2025-08-05 19:43:38,814 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:228)
2025-08-05 19:43:38,814 [INFO] main - ✅ Scraper thread temizlendi (callback) (main.py:231)
2025-08-05 19:43:38,814 [INFO] main - 🔍 Son 1 dakikada toplam 12 kullanıcı: (main.py:248)
2025-08-05 19:43:38,814 [INFO] main -   - legnac.finans: Bekleniyor (2025-08-05 19:43:32) (main.py:250)
2025-08-05 19:43:38,814 [INFO] main -   - mahsundeniz0445: Bekleniyor (2025-08-05 19:43:27) (main.py:250)
2025-08-05 19:43:38,814 [INFO] main -   - kumralim_67: Bekleniyor (2025-08-05 19:43:23) (main.py:250)
2025-08-05 19:43:38,814 [INFO] main -   - muhammedcetinkaya35: Bekleniyor (2025-08-05 19:43:19) (main.py:250)
2025-08-05 19:43:38,814 [INFO] main -   - starfamesfashion: Bekleniyor (2025-08-05 19:43:14) (main.py:250)
2025-08-05 19:43:38,814 [INFO] main -   - vanessa.pm007: Bekleniyor (2025-08-05 19:43:10) (main.py:250)
2025-08-05 19:43:38,814 [INFO] main -   - seiiarsslan: Bekleniyor (2025-08-05 19:43:05) (main.py:250)
2025-08-05 19:43:38,814 [INFO] main -   - afyonlu.0319: Bekleniyor (2025-08-05 19:43:00) (main.py:250)
2025-08-05 19:43:38,814 [INFO] main -   - asinehiri: Bekleniyor (2025-08-05 19:42:55) (main.py:250)
2025-08-05 19:43:38,814 [INFO] main -   - aytekinulu: Bekleniyor (2025-08-05 19:42:51) (main.py:250)
2025-08-05 19:43:38,814 [INFO] main -   - mahmmadullahalkoz: Bekleniyor (2025-08-05 19:42:46) (main.py:250)
2025-08-05 19:43:38,814 [INFO] main -   - mehmetalierbilofficial: Bekleniyor (2025-08-05 19:42:41) (main.py:250)
2025-08-05 19:43:38,814 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:254)
2025-08-05 19:43:38,814 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:373)
2025-08-05 19:43:38,814 [INFO] main - 🔧 Phase lock alınıyor... (main.py:379)
2025-08-05 19:43:38,814 [INFO] main - 🔧 Phase lock alındı (main.py:381)
2025-08-05 19:43:38,814 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:389)
2025-08-05 19:43:38,814 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:392)
2025-08-05 19:43:38,814 [INFO] main - 📋 Kullanıcılar: ['mehmetalierbilofficial', 'mahmmadullahalkoz', 'aytekinulu', 'asinehiri', 'afyonlu.0319', 'seiiarsslan', 'vanessa.pm007', 'starfamesfashion', 'muhammedcetinkaya35', 'kumralim_67', 'mahsundeniz0445', 'legnac.finans'] (main.py:393)
2025-08-05 19:43:38,814 [INFO] main - 📋 Publishers formatı: [{'username': 'mehmetalierbilofficial'}, {'username': 'mahmmadullahalkoz'}, {'username': 'aytekinulu'}, {'username': 'asinehiri'}, {'username': 'afyonlu.0319'}, {'username': 'seiiarsslan'}, {'username': 'vanessa.pm007'}, {'username': 'starfamesfashion'}, {'username': 'muhammedcetinkaya35'}, {'username': 'kumralim_67'}, {'username': 'mahsundeniz0445'}, {'username': 'legnac.finans'}] (main.py:397)
2025-08-05 19:43:38,814 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:399)
2025-08-05 19:43:38,830 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:409)
2025-08-05 19:43:38,830 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:410)
2025-08-05 19:43:38,830 [INFO] main - 🔧 Thread daemon: True (main.py:411)
2025-08-05 19:43:38,830 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:420)
2025-08-05 19:43:38,830 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-08-05 19:43:38,830 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:424)
2025-08-05 19:43:38,830 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 12 (status_checker.py:84)
2025-08-05 19:43:38,830 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:433)
2025-08-05 19:43:38,830 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 9116 (status_checker.py:84)
2025-08-05 19:43:38,830 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-08-05 19:43:38,830 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:43:38,830 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-08-05 19:43:38,830 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-08-05 19:43:41,986 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-08-05 19:43:41,986 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı işlenecek (status_checker.py:84)
2025-08-05 19:43:41,986 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'mehmetalierbilofficial'}, {'username': 'mahmmadullahalkoz'}, {'username': 'aytekinulu'}] (status_checker.py:84)
2025-08-05 19:43:41,986 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-08-05 19:43:41,986 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-08-05 19:43:41,986 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-08-05 19:43:41,986 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-08-05 19:43:41,986 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-08-05 19:43:41,986 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-08-05 19:43:41,986 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-08-05 19:43:41,986 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-08-05 19:43:43,672 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:43:46,684 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-08-05 19:43:46,684 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:43:46,684 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-08-05 19:43:46,684 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-08-05 19:43:51,848 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-08-05 19:43:51,849 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-08-05 19:43:52,138 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-08-05 19:43:52,613 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-08-05 19:43:52,615 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-08-05 19:43:52,616 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-08-05 19:43:55,775 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-08-05 19:43:55,776 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 12 kullanıcı). (status_checker.py:84)
2025-08-05 19:43:58,725 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-08-05 19:44:05,398 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-08-05 19:44:08,380 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-08-05 19:44:10,442 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 12 (status_checker.py:84)
2025-08-05 19:44:10,448 [INFO] StatusChecker - [STATUS_CHECKER] 💾 12 kaydın durumu güncellendi. (status_checker.py:84)
2025-08-05 19:44:13,857 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-08-05 19:44:19,291 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-08-05 19:44:19,416 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-08-05 19:44:19,416 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-08-05 19:44:19,416 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:271)
2025-08-05 19:44:19,416 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:274)
2025-08-05 19:44:19,416 [INFO] main - ✅ Status Checker thread temizlendi (callback) (main.py:277)
2025-08-05 19:44:19,416 [INFO] main - 3️⃣ Message Sender başlatılıyor (9 kullanıcı) (main.py:289)
2025-08-05 19:44:19,416 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:454)
2025-08-05 19:44:19,416 [INFO] main - ✅ Thread temizlendi, scraper başlatılıyor (main.py:458)
2025-08-05 19:44:20,432 [INFO] main - 🔧 start_scraper çağrıldı (main.py:325)
2025-08-05 19:44:20,432 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:327)
2025-08-05 19:44:20,432 [INFO] main - 🔧 Phase lock alındı (main.py:330)
2025-08-05 19:44:20,432 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:347)
2025-08-05 19:44:20,432 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:350)
2025-08-05 19:44:20,432 [INFO] main - ✅ Scraper thread başlatıldı (main.py:364)
2025-08-05 19:44:24,876 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-08-05 19:44:24,876 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-08-05 19:44:31,752 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-08-05 19:44:36,732 [INFO] scraper_thread - [SCRAPER] mehmetalierbilofficial | 939 (scraper_thread.py:321)
2025-08-05 19:44:41,802 [INFO] scraper_thread - [SCRAPER] bana.iyi.gelecek | 0 (scraper_thread.py:321)
2025-08-05 19:44:46,760 [INFO] scraper_thread - [SCRAPER] aleme__inat | 4 (scraper_thread.py:321)
2025-08-05 19:44:52,519 [INFO] scraper_thread - [SCRAPER] gozuyasliadem | 13 (scraper_thread.py:321)
2025-08-05 19:44:57,777 [INFO] scraper_thread - [SCRAPER] wwwwwwwwwwwwwww920 | 2 (scraper_thread.py:321)
2025-08-05 19:45:00,145 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-08-05 19:45:02,952 [INFO] scraper_thread - [SCRAPER] izollu_tekin | 6 (scraper_thread.py:321)
2025-08-05 19:45:08,090 [INFO] scraper_thread - [SCRAPER] wxyasir | 65 (scraper_thread.py:321)
2025-08-05 19:45:12,240 [INFO] scraper_thread - [SCRAPER] fark34_etmezlerdiyim | 2 (scraper_thread.py:321)
2025-08-05 19:45:17,733 [INFO] scraper_thread - [SCRAPER] abuseyran02 | 2 (scraper_thread.py:321)
2025-08-05 19:45:21,595 [INFO] scraper_thread - [SCRAPER] youtube_burakk | 5 (scraper_thread.py:321)
2025-08-05 19:45:25,664 [INFO] scraper_thread - [SCRAPER] ilbeyli_58 | 0 (scraper_thread.py:321)
2025-08-05 19:45:30,221 [INFO] scraper_thread - [SCRAPER] coskun.kederli | 3 (scraper_thread.py:321)
2025-08-05 19:45:30,310 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-08-05 19:45:34,274 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 12 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-08-05 19:45:36,845 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-08-05 19:45:36,845 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:225)
2025-08-05 19:45:36,845 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:228)
2025-08-05 19:45:36,845 [INFO] main - ✅ Scraper thread temizlendi (callback) (main.py:231)
2025-08-05 19:45:36,845 [INFO] main - 🔍 Son 1 dakikada toplam 12 kullanıcı: (main.py:248)
2025-08-05 19:45:36,845 [INFO] main -   - coskun.kederli: Bekleniyor (2025-08-05 19:45:30) (main.py:250)
2025-08-05 19:45:36,845 [INFO] main -   - ilbeyli_58: Bekleniyor (2025-08-05 19:45:25) (main.py:250)
2025-08-05 19:45:36,845 [INFO] main -   - youtube_burakk: Bekleniyor (2025-08-05 19:45:21) (main.py:250)
2025-08-05 19:45:36,845 [INFO] main -   - abuseyran02: Bekleniyor (2025-08-05 19:45:17) (main.py:250)
2025-08-05 19:45:36,845 [INFO] main -   - fark34_etmezlerdiyim: Bekleniyor (2025-08-05 19:45:12) (main.py:250)
2025-08-05 19:45:36,845 [INFO] main -   - wxyasir: Bekleniyor (2025-08-05 19:45:08) (main.py:250)
2025-08-05 19:45:36,845 [INFO] main -   - izollu_tekin: Bekleniyor (2025-08-05 19:45:02) (main.py:250)
2025-08-05 19:45:36,845 [INFO] main -   - wwwwwwwwwwwwwww920: Bekleniyor (2025-08-05 19:44:57) (main.py:250)
2025-08-05 19:45:36,845 [INFO] main -   - gozuyasliadem: Bekleniyor (2025-08-05 19:44:52) (main.py:250)
2025-08-05 19:45:36,845 [INFO] main -   - aleme__inat: Bekleniyor (2025-08-05 19:44:46) (main.py:250)
2025-08-05 19:45:36,845 [INFO] main -   - bana.iyi.gelecek: Bekleniyor (2025-08-05 19:44:41) (main.py:250)
2025-08-05 19:45:36,845 [INFO] main -   - mehmetalierbilofficial: Bekleniyor (2025-08-05 19:44:36) (main.py:250)
2025-08-05 19:45:36,845 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:254)
2025-08-05 19:45:36,845 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:373)
2025-08-05 19:45:36,845 [INFO] main - 🔧 Phase lock alınıyor... (main.py:379)
2025-08-05 19:45:36,845 [INFO] main - 🔧 Phase lock alındı (main.py:381)
2025-08-05 19:45:36,845 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:389)
2025-08-05 19:45:36,845 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:392)
2025-08-05 19:45:36,845 [INFO] main - 📋 Kullanıcılar: ['mehmetalierbilofficial', 'bana.iyi.gelecek', 'aleme__inat', 'gozuyasliadem', 'wwwwwwwwwwwwwww920', 'izollu_tekin', 'wxyasir', 'fark34_etmezlerdiyim', 'abuseyran02', 'youtube_burakk', 'ilbeyli_58', 'coskun.kederli'] (main.py:393)
2025-08-05 19:45:36,845 [INFO] main - 📋 Publishers formatı: [{'username': 'mehmetalierbilofficial'}, {'username': 'bana.iyi.gelecek'}, {'username': 'aleme__inat'}, {'username': 'gozuyasliadem'}, {'username': 'wwwwwwwwwwwwwww920'}, {'username': 'izollu_tekin'}, {'username': 'wxyasir'}, {'username': 'fark34_etmezlerdiyim'}, {'username': 'abuseyran02'}, {'username': 'youtube_burakk'}, {'username': 'ilbeyli_58'}, {'username': 'coskun.kederli'}] (main.py:397)
2025-08-05 19:45:36,845 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:399)
2025-08-05 19:45:36,845 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:409)
2025-08-05 19:45:36,845 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:410)
2025-08-05 19:45:36,861 [INFO] main - 🔧 Thread daemon: True (main.py:411)
2025-08-05 19:45:36,861 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:420)
2025-08-05 19:45:36,861 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-08-05 19:45:36,861 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:424)
2025-08-05 19:45:36,861 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 12 (status_checker.py:84)
2025-08-05 19:45:36,861 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:433)
2025-08-05 19:45:36,861 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 6772 (status_checker.py:84)
2025-08-05 19:45:36,861 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-08-05 19:45:36,861 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:45:36,861 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-08-05 19:45:36,861 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-08-05 19:45:40,033 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-08-05 19:45:40,033 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı işlenecek (status_checker.py:84)
2025-08-05 19:45:40,033 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'mehmetalierbilofficial'}, {'username': 'bana.iyi.gelecek'}, {'username': 'aleme__inat'}] (status_checker.py:84)
2025-08-05 19:45:40,033 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-08-05 19:45:40,033 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-08-05 19:45:40,033 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-08-05 19:45:40,033 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-08-05 19:45:40,033 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-08-05 19:45:40,033 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-08-05 19:45:40,033 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-08-05 19:45:40,033 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-08-05 19:45:41,656 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:45:44,663 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-08-05 19:45:44,663 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:45:44,663 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-08-05 19:45:44,663 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-08-05 19:45:47,342 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-08-05 19:45:47,345 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-08-05 19:45:49,025 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-08-05 19:45:49,733 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-08-05 19:45:49,737 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-08-05 19:45:49,738 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-08-05 19:45:53,126 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-08-05 19:45:53,126 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 12 kullanıcı). (status_checker.py:84)
2025-08-05 19:45:55,758 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-08-05 19:46:00,546 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-08-05 19:46:01,157 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-08-05 19:46:04,297 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-08-05 19:46:05,979 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 12 (status_checker.py:84)
2025-08-05 19:46:05,995 [INFO] StatusChecker - [STATUS_CHECKER] 💾 12 kaydın durumu güncellendi. (status_checker.py:84)
2025-08-05 19:46:08,854 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-08-05 19:46:13,293 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-08-05 19:46:13,433 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-08-05 19:46:13,433 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-08-05 19:46:13,433 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:271)
2025-08-05 19:46:13,433 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:274)
2025-08-05 19:46:13,433 [INFO] main - ✅ Status Checker thread temizlendi (callback) (main.py:277)
2025-08-05 19:46:13,433 [INFO] main - 3️⃣ Message Sender başlatılıyor (10 kullanıcı) (main.py:289)
2025-08-05 19:46:13,433 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:454)
2025-08-05 19:46:13,433 [INFO] main - ✅ Thread temizlendi, scraper başlatılıyor (main.py:458)
2025-08-05 19:46:14,449 [INFO] main - 🔧 start_scraper çağrıldı (main.py:325)
2025-08-05 19:46:14,449 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:327)
2025-08-05 19:46:14,449 [INFO] main - 🔧 Phase lock alındı (main.py:330)
2025-08-05 19:46:14,449 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:347)
2025-08-05 19:46:14,449 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:350)
2025-08-05 19:46:14,449 [INFO] main - ✅ Scraper thread başlatıldı (main.py:364)
2025-08-05 19:46:18,445 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-08-05 19:46:18,446 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-08-05 19:46:24,770 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-08-05 19:46:28,410 [INFO] scraper_thread - [SCRAPER] mehmetalierbilofficial | 913 (scraper_thread.py:321)
2025-08-05 19:46:30,724 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-08-05 19:46:37,403 [INFO] scraper_thread - [SCRAPER] welat_y_65 | 3 (scraper_thread.py:321)
2025-08-05 19:46:42,059 [INFO] scraper_thread - [SCRAPER] kuzeybey13 | 1 (scraper_thread.py:321)
2025-08-05 19:46:46,908 [INFO] scraper_thread - [SCRAPER] sen.emoo41 | 1 (scraper_thread.py:321)
2025-08-05 19:46:50,760 [INFO] scraper_thread - [SCRAPER] semo_gnrr | 4 (scraper_thread.py:321)
2025-08-05 19:46:55,588 [INFO] scraper_thread - [SCRAPER] aliisikofficial | 935 (scraper_thread.py:321)
2025-08-05 19:47:00,382 [INFO] scraper_thread - [SCRAPER] uur.esedov.19.03.2 | 0 (scraper_thread.py:321)
2025-08-05 19:47:00,965 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-08-05 19:47:05,179 [INFO] scraper_thread - [SCRAPER] ciyage21 | 0 (scraper_thread.py:321)
2025-08-05 19:47:09,274 [INFO] scraper_thread - [SCRAPER] bir_bilsen_78 | 2 (scraper_thread.py:321)
2025-08-05 19:47:13,353 [INFO] scraper_thread - [SCRAPER] user934833243 | 1 (scraper_thread.py:321)
2025-08-05 19:47:18,010 [INFO] scraper_thread - [SCRAPER] b.bulut7 | 3 (scraper_thread.py:321)
2025-08-05 19:47:22,911 [INFO] scraper_thread - [SCRAPER] berat2858 | 3 (scraper_thread.py:321)
2025-08-05 19:47:26,829 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 12 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-08-05 19:47:29,291 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-08-05 19:47:29,291 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:225)
2025-08-05 19:47:29,291 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:228)
2025-08-05 19:47:29,291 [INFO] main - ✅ Scraper thread temizlendi (callback) (main.py:231)
2025-08-05 19:47:29,291 [INFO] main - 🔍 Son 1 dakikada toplam 11 kullanıcı: (main.py:248)
2025-08-05 19:47:29,291 [INFO] main -   - berat2858: Bekleniyor (2025-08-05 19:47:22) (main.py:250)
2025-08-05 19:47:29,291 [INFO] main -   - b.bulut7: Bekleniyor (2025-08-05 19:47:18) (main.py:250)
2025-08-05 19:47:29,291 [INFO] main -   - user934833243: Bekleniyor (2025-08-05 19:47:13) (main.py:250)
2025-08-05 19:47:29,291 [INFO] main -   - bir_bilsen_78: Bekleniyor (2025-08-05 19:47:09) (main.py:250)
2025-08-05 19:47:29,291 [INFO] main -   - ciyage21: Bekleniyor (2025-08-05 19:47:05) (main.py:250)
2025-08-05 19:47:29,291 [INFO] main -   - uur.esedov.19.03.2: Bekleniyor (2025-08-05 19:47:00) (main.py:250)
2025-08-05 19:47:29,291 [INFO] main -   - aliisikofficial: Bekleniyor (2025-08-05 19:46:55) (main.py:250)
2025-08-05 19:47:29,291 [INFO] main -   - semo_gnrr: Bekleniyor (2025-08-05 19:46:50) (main.py:250)
2025-08-05 19:47:29,291 [INFO] main -   - sen.emoo41: Bekleniyor (2025-08-05 19:46:46) (main.py:250)
2025-08-05 19:47:29,291 [INFO] main -   - kuzeybey13: Bekleniyor (2025-08-05 19:46:42) (main.py:250)
2025-08-05 19:47:29,291 [INFO] main -   - welat_y_65: Bekleniyor (2025-08-05 19:46:37) (main.py:250)
2025-08-05 19:47:29,291 [INFO] main - 2️⃣ Status Checker başlatılıyor (11 kullanıcı) (main.py:254)
2025-08-05 19:47:29,291 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:373)
2025-08-05 19:47:29,291 [INFO] main - 🔧 Phase lock alınıyor... (main.py:379)
2025-08-05 19:47:29,291 [INFO] main - 🔧 Phase lock alındı (main.py:381)
2025-08-05 19:47:29,291 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:389)
2025-08-05 19:47:29,291 [INFO] main - 2️⃣ Status Checker başlatılıyor (11 kullanıcı) (main.py:392)
2025-08-05 19:47:29,291 [INFO] main - 📋 Kullanıcılar: ['welat_y_65', 'kuzeybey13', 'sen.emoo41', 'semo_gnrr', 'aliisikofficial', 'uur.esedov.19.03.2', 'ciyage21', 'bir_bilsen_78', 'user934833243', 'b.bulut7', 'berat2858'] (main.py:393)
2025-08-05 19:47:29,291 [INFO] main - 📋 Publishers formatı: [{'username': 'welat_y_65'}, {'username': 'kuzeybey13'}, {'username': 'sen.emoo41'}, {'username': 'semo_gnrr'}, {'username': 'aliisikofficial'}, {'username': 'uur.esedov.19.03.2'}, {'username': 'ciyage21'}, {'username': 'bir_bilsen_78'}, {'username': 'user934833243'}, {'username': 'b.bulut7'}, {'username': 'berat2858'}] (main.py:397)
2025-08-05 19:47:29,291 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:399)
2025-08-05 19:47:29,291 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:409)
2025-08-05 19:47:29,291 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:410)
2025-08-05 19:47:29,307 [INFO] main - 🔧 Thread daemon: True (main.py:411)
2025-08-05 19:47:29,307 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:420)
2025-08-05 19:47:29,307 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-08-05 19:47:29,307 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:424)
2025-08-05 19:47:29,307 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 11 (status_checker.py:84)
2025-08-05 19:47:29,307 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:433)
2025-08-05 19:47:29,307 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 2488 (status_checker.py:84)
2025-08-05 19:47:29,307 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-08-05 19:47:29,307 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:47:29,307 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-08-05 19:47:29,307 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-08-05 19:47:32,463 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-08-05 19:47:32,463 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 11 kullanıcı işlenecek (status_checker.py:84)
2025-08-05 19:47:32,463 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'welat_y_65'}, {'username': 'kuzeybey13'}, {'username': 'sen.emoo41'}] (status_checker.py:84)
2025-08-05 19:47:32,463 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-08-05 19:47:32,463 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-08-05 19:47:32,463 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-08-05 19:47:32,463 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-08-05 19:47:32,463 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-08-05 19:47:32,463 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-08-05 19:47:32,463 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-08-05 19:47:32,463 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-08-05 19:47:34,147 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:47:37,157 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-08-05 19:47:37,157 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:47:37,157 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-08-05 19:47:37,157 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-08-05 19:47:39,147 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-08-05 19:47:39,151 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-08-05 19:47:40,223 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-08-05 19:47:41,721 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-08-05 19:47:41,722 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-08-05 19:47:41,724 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-08-05 19:47:45,087 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-08-05 19:47:45,087 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 11 kullanıcı). (status_checker.py:84)
2025-08-05 19:47:48,021 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-08-05 19:47:52,853 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 11 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-08-05 19:47:55,882 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-08-05 19:47:57,963 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 11 (status_checker.py:84)
2025-08-05 19:47:57,968 [INFO] StatusChecker - [STATUS_CHECKER] 💾 11 kaydın durumu güncellendi. (status_checker.py:84)
2025-08-05 19:48:01,538 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-08-05 19:48:06,986 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-08-05 19:48:07,126 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-08-05 19:48:07,126 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-08-05 19:48:07,126 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:271)
2025-08-05 19:48:07,126 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:274)
2025-08-05 19:48:07,126 [INFO] main - ✅ Status Checker thread temizlendi (callback) (main.py:277)
2025-08-05 19:48:07,126 [INFO] main - 3️⃣ Message Sender başlatılıyor (10 kullanıcı) (main.py:289)
2025-08-05 19:48:07,126 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:454)
2025-08-05 19:48:07,126 [INFO] main - ✅ Thread temizlendi, scraper başlatılıyor (main.py:458)
2025-08-05 19:48:08,142 [INFO] main - 🔧 start_scraper çağrıldı (main.py:325)
2025-08-05 19:48:08,142 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:327)
2025-08-05 19:48:08,142 [INFO] main - 🔧 Phase lock alındı (main.py:330)
2025-08-05 19:48:08,142 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:347)
2025-08-05 19:48:08,142 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:350)
2025-08-05 19:48:08,142 [INFO] main - ✅ Scraper thread başlatıldı (main.py:364)
2025-08-05 19:48:12,526 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-08-05 19:48:12,526 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-08-05 19:48:19,566 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-08-05 19:48:24,926 [INFO] scraper_thread - [SCRAPER] mehmetalierbilofficial | 859 (scraper_thread.py:321)
2025-08-05 19:48:29,113 [INFO] scraper_thread - [SCRAPER] heybatmanli72 | 3 (scraper_thread.py:321)
2025-08-05 19:48:33,826 [INFO] scraper_thread - [SCRAPER] deepcacaxxl | 71 (scraper_thread.py:321)
2025-08-05 19:48:38,354 [INFO] scraper_thread - [SCRAPER] cilginmikrofonn | 100 (scraper_thread.py:321)
2025-08-05 19:48:42,796 [INFO] scraper_thread - [SCRAPER] guler58erxdem | 6 (scraper_thread.py:321)
2025-08-05 19:48:48,109 [INFO] scraper_thread - [SCRAPER] dogukanbucaa35 | 26 (scraper_thread.py:321)
2025-08-05 19:48:52,780 [INFO] scraper_thread - [SCRAPER] akkurt096 | 0 (scraper_thread.py:321)
2025-08-05 19:48:57,459 [INFO] scraper_thread - [SCRAPER] trno1fanhesabi | 6 (scraper_thread.py:321)
2025-08-05 19:49:01,708 [INFO] scraper_thread - [SCRAPER] vanessa.pm007 | 70 (scraper_thread.py:321)
2025-08-05 19:49:06,239 [INFO] scraper_thread - [SCRAPER] userme72.12 | 0 (scraper_thread.py:321)
2025-08-05 19:49:11,761 [INFO] scraper_thread - [SCRAPER] trader.man.x | 83 (scraper_thread.py:321)
2025-08-05 19:49:17,348 [INFO] scraper_thread - [SCRAPER] onur.can.gzel.tab8 | 100 (scraper_thread.py:321)
2025-08-05 19:49:21,904 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 12 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-08-05 19:49:24,358 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-08-05 19:49:24,358 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:225)
2025-08-05 19:49:24,358 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:228)
2025-08-05 19:49:24,358 [INFO] main - ✅ Scraper thread temizlendi (callback) (main.py:231)
2025-08-05 19:49:24,358 [INFO] main - 🔍 Son 1 dakikada toplam 12 kullanıcı: (main.py:248)
2025-08-05 19:49:24,358 [INFO] main -   - onur.can.gzel.tab8: Bekleniyor (2025-08-05 19:49:17) (main.py:250)
2025-08-05 19:49:24,358 [INFO] main -   - trader.man.x: Bekleniyor (2025-08-05 19:49:11) (main.py:250)
2025-08-05 19:49:24,358 [INFO] main -   - userme72.12: Bekleniyor (2025-08-05 19:49:06) (main.py:250)
2025-08-05 19:49:24,358 [INFO] main -   - vanessa.pm007: Bekleniyor (2025-08-05 19:49:01) (main.py:250)
2025-08-05 19:49:24,358 [INFO] main -   - trno1fanhesabi: Bekleniyor (2025-08-05 19:48:57) (main.py:250)
2025-08-05 19:49:24,358 [INFO] main -   - akkurt096: Bekleniyor (2025-08-05 19:48:52) (main.py:250)
2025-08-05 19:49:24,358 [INFO] main -   - dogukanbucaa35: Bekleniyor (2025-08-05 19:48:48) (main.py:250)
2025-08-05 19:49:24,358 [INFO] main -   - guler58erxdem: Bekleniyor (2025-08-05 19:48:42) (main.py:250)
2025-08-05 19:49:24,358 [INFO] main -   - cilginmikrofonn: Bekleniyor (2025-08-05 19:48:38) (main.py:250)
2025-08-05 19:49:24,358 [INFO] main -   - deepcacaxxl: Bekleniyor (2025-08-05 19:48:33) (main.py:250)
2025-08-05 19:49:24,358 [INFO] main -   - heybatmanli72: Bekleniyor (2025-08-05 19:48:29) (main.py:250)
2025-08-05 19:49:24,358 [INFO] main -   - mehmetalierbilofficial: Bekleniyor (2025-08-05 19:48:24) (main.py:250)
2025-08-05 19:49:24,358 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:254)
2025-08-05 19:49:24,358 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:373)
2025-08-05 19:49:24,358 [INFO] main - 🔧 Phase lock alınıyor... (main.py:379)
2025-08-05 19:49:24,358 [INFO] main - 🔧 Phase lock alındı (main.py:381)
2025-08-05 19:49:24,358 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:389)
2025-08-05 19:49:24,358 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:392)
2025-08-05 19:49:24,358 [INFO] main - 📋 Kullanıcılar: ['mehmetalierbilofficial', 'heybatmanli72', 'deepcacaxxl', 'cilginmikrofonn', 'guler58erxdem', 'dogukanbucaa35', 'akkurt096', 'trno1fanhesabi', 'vanessa.pm007', 'userme72.12', 'trader.man.x', 'onur.can.gzel.tab8'] (main.py:393)
2025-08-05 19:49:24,358 [INFO] main - 📋 Publishers formatı: [{'username': 'mehmetalierbilofficial'}, {'username': 'heybatmanli72'}, {'username': 'deepcacaxxl'}, {'username': 'cilginmikrofonn'}, {'username': 'guler58erxdem'}, {'username': 'dogukanbucaa35'}, {'username': 'akkurt096'}, {'username': 'trno1fanhesabi'}, {'username': 'vanessa.pm007'}, {'username': 'userme72.12'}, {'username': 'trader.man.x'}, {'username': 'onur.can.gzel.tab8'}] (main.py:397)
2025-08-05 19:49:24,374 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:399)
2025-08-05 19:49:24,374 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:409)
2025-08-05 19:49:24,374 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:410)
2025-08-05 19:49:24,374 [INFO] main - 🔧 Thread daemon: True (main.py:411)
2025-08-05 19:49:24,374 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:420)
2025-08-05 19:49:24,374 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-08-05 19:49:24,374 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:424)
2025-08-05 19:49:24,374 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 12 (status_checker.py:84)
2025-08-05 19:49:24,374 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:433)
2025-08-05 19:49:24,374 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 576 (status_checker.py:84)
2025-08-05 19:49:24,374 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-08-05 19:49:24,374 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:49:24,374 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-08-05 19:49:24,374 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-08-05 19:49:27,545 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-08-05 19:49:27,545 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı işlenecek (status_checker.py:84)
2025-08-05 19:49:27,545 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'mehmetalierbilofficial'}, {'username': 'heybatmanli72'}, {'username': 'deepcacaxxl'}] (status_checker.py:84)
2025-08-05 19:49:27,545 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-08-05 19:49:27,545 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-08-05 19:49:27,545 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-08-05 19:49:27,545 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-08-05 19:49:27,545 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-08-05 19:49:27,545 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-08-05 19:49:27,545 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-08-05 19:49:27,545 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-08-05 19:49:29,288 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:49:32,296 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-08-05 19:49:32,296 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:49:32,296 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-08-05 19:49:32,296 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-08-05 19:49:37,286 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-08-05 19:49:37,299 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-08-05 19:49:37,405 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-08-05 19:49:37,421 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-08-05 19:49:37,422 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-08-05 19:49:37,422 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-08-05 19:49:40,724 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-08-05 19:49:40,724 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 12 kullanıcı). (status_checker.py:84)
2025-08-05 19:49:43,172 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-08-05 19:49:48,521 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-08-05 19:49:52,230 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-08-05 19:49:53,787 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 12 (status_checker.py:84)
2025-08-05 19:49:53,792 [INFO] StatusChecker - [STATUS_CHECKER] 💾 12 kaydın durumu güncellendi. (status_checker.py:84)
2025-08-05 19:49:57,339 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-08-05 19:50:01,741 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-08-05 19:50:01,882 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-08-05 19:50:01,882 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-08-05 19:50:01,882 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:271)
2025-08-05 19:50:01,882 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:274)
2025-08-05 19:50:01,882 [INFO] main - ✅ Status Checker thread temizlendi (callback) (main.py:277)
2025-08-05 19:50:01,882 [INFO] main - 3️⃣ Message Sender başlatılıyor (7 kullanıcı) (main.py:289)
2025-08-05 19:50:01,882 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:454)
2025-08-05 19:50:01,882 [INFO] main - ✅ Thread temizlendi, scraper başlatılıyor (main.py:458)
2025-08-05 19:50:02,897 [INFO] main - 🔧 start_scraper çağrıldı (main.py:325)
2025-08-05 19:50:02,897 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:327)
2025-08-05 19:50:02,897 [INFO] main - 🔧 Phase lock alındı (main.py:330)
2025-08-05 19:50:02,897 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:347)
2025-08-05 19:50:02,897 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:350)
2025-08-05 19:50:02,897 [INFO] main - ✅ Scraper thread başlatıldı (main.py:364)
2025-08-05 19:50:06,961 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-08-05 19:50:06,962 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-08-05 19:50:14,651 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-08-05 19:50:20,775 [INFO] scraper_thread - [SCRAPER] mehmetalierbilofficial | 811 (scraper_thread.py:321)
2025-08-05 19:50:24,996 [INFO] scraper_thread - [SCRAPER] bakininciftligi | 9 (scraper_thread.py:321)
2025-08-05 19:50:29,672 [INFO] scraper_thread - [SCRAPER] abdulkadir.iti | 2 (scraper_thread.py:321)
2025-08-05 19:50:30,211 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-08-05 19:50:33,698 [INFO] scraper_thread - [SCRAPER] bio..gaming | 3 (scraper_thread.py:321)
2025-08-05 19:50:39,052 [INFO] scraper_thread - [SCRAPER] ayaz_kaan.79 | 0 (scraper_thread.py:321)
2025-08-05 19:50:44,619 [INFO] scraper_thread - [SCRAPER] legnac.finans | 18 (scraper_thread.py:321)
2025-08-05 19:50:49,960 [INFO] scraper_thread - [SCRAPER] muhammed.denniz | 44 (scraper_thread.py:321)
2025-08-05 19:50:55,422 [INFO] scraper_thread - [SCRAPER] mustafatereci51 | 100 (scraper_thread.py:321)
2025-08-05 19:50:59,860 [INFO] scraper_thread - [SCRAPER] bilal71sarsilmaz | 2 (scraper_thread.py:321)
2025-08-05 19:51:00,371 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-08-05 19:51:04,879 [INFO] scraper_thread - [SCRAPER] r4w.esports | 50 (scraper_thread.py:321)
2025-08-05 19:51:09,204 [INFO] scraper_thread - [SCRAPER] tt.loya.34 | 6 (scraper_thread.py:321)
2025-08-05 19:51:13,224 [INFO] scraper_thread - [SCRAPER] ramazancaliskan2121 | 2 (scraper_thread.py:321)
2025-08-05 19:51:18,383 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 12 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-08-05 19:51:20,965 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-08-05 19:51:20,965 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:225)
2025-08-05 19:51:20,965 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:228)
2025-08-05 19:51:20,965 [INFO] main - ✅ Scraper thread temizlendi (callback) (main.py:231)
2025-08-05 19:51:20,965 [INFO] main - 🔍 Son 1 dakikada toplam 12 kullanıcı: (main.py:248)
2025-08-05 19:51:20,965 [INFO] main -   - ramazancaliskan2121: Bekleniyor (2025-08-05 19:51:13) (main.py:250)
2025-08-05 19:51:20,965 [INFO] main -   - tt.loya.34: Bekleniyor (2025-08-05 19:51:09) (main.py:250)
2025-08-05 19:51:20,965 [INFO] main -   - r4w.esports: Bekleniyor (2025-08-05 19:51:04) (main.py:250)
2025-08-05 19:51:20,965 [INFO] main -   - bilal71sarsilmaz: Bekleniyor (2025-08-05 19:50:59) (main.py:250)
2025-08-05 19:51:20,965 [INFO] main -   - mustafatereci51: Bekleniyor (2025-08-05 19:50:55) (main.py:250)
2025-08-05 19:51:20,965 [INFO] main -   - muhammed.denniz: Bekleniyor (2025-08-05 19:50:49) (main.py:250)
2025-08-05 19:51:20,965 [INFO] main -   - legnac.finans: Bekleniyor (2025-08-05 19:50:44) (main.py:250)
2025-08-05 19:51:20,965 [INFO] main -   - ayaz_kaan.79: Bekleniyor (2025-08-05 19:50:39) (main.py:250)
2025-08-05 19:51:20,965 [INFO] main -   - bio..gaming: Bekleniyor (2025-08-05 19:50:33) (main.py:250)
2025-08-05 19:51:20,965 [INFO] main -   - abdulkadir.iti: Bekleniyor (2025-08-05 19:50:29) (main.py:250)
2025-08-05 19:51:20,965 [INFO] main -   - bakininciftligi: Bekleniyor (2025-08-05 19:50:24) (main.py:250)
2025-08-05 19:51:20,965 [INFO] main -   - mehmetalierbilofficial: Bekleniyor (2025-08-05 19:50:20) (main.py:250)
2025-08-05 19:51:20,965 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:254)
2025-08-05 19:51:20,965 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:373)
2025-08-05 19:51:20,965 [INFO] main - 🔧 Phase lock alınıyor... (main.py:379)
2025-08-05 19:51:20,965 [INFO] main - 🔧 Phase lock alındı (main.py:381)
2025-08-05 19:51:20,965 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:389)
2025-08-05 19:51:20,965 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:392)
2025-08-05 19:51:20,965 [INFO] main - 📋 Kullanıcılar: ['mehmetalierbilofficial', 'bakininciftligi', 'abdulkadir.iti', 'bio..gaming', 'ayaz_kaan.79', 'legnac.finans', 'muhammed.denniz', 'mustafatereci51', 'bilal71sarsilmaz', 'r4w.esports', 'tt.loya.34', 'ramazancaliskan2121'] (main.py:393)
2025-08-05 19:51:20,965 [INFO] main - 📋 Publishers formatı: [{'username': 'mehmetalierbilofficial'}, {'username': 'bakininciftligi'}, {'username': 'abdulkadir.iti'}, {'username': 'bio..gaming'}, {'username': 'ayaz_kaan.79'}, {'username': 'legnac.finans'}, {'username': 'muhammed.denniz'}, {'username': 'mustafatereci51'}, {'username': 'bilal71sarsilmaz'}, {'username': 'r4w.esports'}, {'username': 'tt.loya.34'}, {'username': 'ramazancaliskan2121'}] (main.py:397)
2025-08-05 19:51:20,965 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:399)
2025-08-05 19:51:20,965 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:409)
2025-08-05 19:51:20,965 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:410)
2025-08-05 19:51:20,980 [INFO] main - 🔧 Thread daemon: True (main.py:411)
2025-08-05 19:51:20,980 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:420)
2025-08-05 19:51:20,980 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-08-05 19:51:20,980 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:424)
2025-08-05 19:51:20,980 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 12 (status_checker.py:84)
2025-08-05 19:51:20,980 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:433)
2025-08-05 19:51:20,980 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 6276 (status_checker.py:84)
2025-08-05 19:51:20,980 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-08-05 19:51:20,980 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:51:20,980 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-08-05 19:51:20,980 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-08-05 19:51:24,137 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-08-05 19:51:24,137 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı işlenecek (status_checker.py:84)
2025-08-05 19:51:24,137 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'mehmetalierbilofficial'}, {'username': 'bakininciftligi'}, {'username': 'abdulkadir.iti'}] (status_checker.py:84)
2025-08-05 19:51:24,137 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-08-05 19:51:24,137 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-08-05 19:51:24,137 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-08-05 19:51:24,137 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-08-05 19:51:24,137 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-08-05 19:51:24,137 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-08-05 19:51:24,137 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-08-05 19:51:24,137 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-08-05 19:51:26,096 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:51:29,167 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-08-05 19:51:29,168 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:51:29,169 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-08-05 19:51:29,171 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-08-05 19:51:30,503 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-08-05 19:51:33,811 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-08-05 19:51:33,816 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-08-05 19:51:33,843 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-08-05 19:51:33,851 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-08-05 19:51:33,852 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-08-05 19:51:33,852 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-08-05 19:51:37,264 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-08-05 19:51:37,264 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 12 kullanıcı). (status_checker.py:84)
2025-08-05 19:51:40,908 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-08-05 19:51:46,429 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-08-05 19:51:49,588 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-08-05 19:51:50,772 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 12 (status_checker.py:84)
2025-08-05 19:51:50,777 [INFO] StatusChecker - [STATUS_CHECKER] 💾 12 kaydın durumu güncellendi. (status_checker.py:84)
2025-08-05 19:51:54,453 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-08-05 19:51:59,885 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-08-05 19:52:00,026 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-08-05 19:52:00,026 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-08-05 19:52:00,026 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:271)
2025-08-05 19:52:00,026 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:274)
2025-08-05 19:52:00,026 [INFO] main - ✅ Status Checker thread temizlendi (callback) (main.py:277)
2025-08-05 19:52:00,026 [INFO] main - 3️⃣ Message Sender başlatılıyor (9 kullanıcı) (main.py:289)
2025-08-05 19:52:00,026 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:454)
2025-08-05 19:52:00,026 [INFO] main - ✅ Thread temizlendi, scraper başlatılıyor (main.py:458)
2025-08-05 19:52:01,042 [INFO] main - 🔧 start_scraper çağrıldı (main.py:325)
2025-08-05 19:52:01,042 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:327)
2025-08-05 19:52:01,042 [INFO] main - 🔧 Phase lock alındı (main.py:330)
2025-08-05 19:52:01,042 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:347)
2025-08-05 19:52:01,042 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:350)
2025-08-05 19:52:01,042 [INFO] main - ✅ Scraper thread başlatıldı (main.py:364)
2025-08-05 19:52:05,076 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-08-05 19:52:05,076 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-08-05 19:52:14,573 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-08-05 19:52:19,590 [INFO] scraper_thread - [SCRAPER] mehmetalierbilofficial | 831 (scraper_thread.py:321)
2025-08-05 19:52:23,568 [INFO] scraper_thread - [SCRAPER] kalisdir5 | 1 (scraper_thread.py:321)
2025-08-05 19:52:28,833 [INFO] scraper_thread - [SCRAPER] ismail_.goren76 | 3 (scraper_thread.py:321)
2025-08-05 19:52:30,839 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-08-05 19:52:34,360 [INFO] scraper_thread - [SCRAPER] bibiseydicem | 68 (scraper_thread.py:321)
2025-08-05 19:52:38,138 [INFO] scraper_thread - [SCRAPER] levo190344 | 0 (scraper_thread.py:321)
2025-08-05 19:52:41,958 [INFO] scraper_thread - [SCRAPER] vindotrxgoe | 7 (scraper_thread.py:321)
2025-08-05 19:52:46,828 [INFO] scraper_thread - [SCRAPER] ismailislami2 | 5 (scraper_thread.py:321)
2025-08-05 19:52:52,558 [INFO] scraper_thread - [SCRAPER] memoo2707 | 2 (scraper_thread.py:321)
2025-08-05 19:52:56,467 [INFO] scraper_thread - [SCRAPER] yldz.edal | 313 (scraper_thread.py:321)
2025-08-05 19:53:00,373 [INFO] scraper_thread - [SCRAPER] kod_3545 | 4 (scraper_thread.py:321)
2025-08-05 19:53:04,356 [INFO] scraper_thread - [SCRAPER] 25yagz.efe25 | 3 (scraper_thread.py:321)
2025-08-05 19:53:08,804 [INFO] scraper_thread - [SCRAPER] seiiarsslan | 1 (scraper_thread.py:321)
2025-08-05 19:53:12,541 [INFO] scraper_thread - [SCRAPER] pubgi.hatun | 22 (scraper_thread.py:321)
2025-08-05 19:53:16,893 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 13 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-08-05 19:53:19,427 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-08-05 19:53:19,427 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:225)
2025-08-05 19:53:19,427 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:228)
2025-08-05 19:53:19,427 [INFO] main - ✅ Scraper thread temizlendi (callback) (main.py:231)
2025-08-05 19:53:19,427 [INFO] main - 🔍 Son 1 dakikada toplam 13 kullanıcı: (main.py:248)
2025-08-05 19:53:19,427 [INFO] main -   - pubgi.hatun: Bekleniyor (2025-08-05 19:53:12) (main.py:250)
2025-08-05 19:53:19,427 [INFO] main -   - seiiarsslan: Bekleniyor (2025-08-05 19:53:08) (main.py:250)
2025-08-05 19:53:19,427 [INFO] main -   - 25yagz.efe25: Bekleniyor (2025-08-05 19:53:04) (main.py:250)
2025-08-05 19:53:19,427 [INFO] main -   - kod_3545: Bekleniyor (2025-08-05 19:53:00) (main.py:250)
2025-08-05 19:53:19,427 [INFO] main -   - yldz.edal: Bekleniyor (2025-08-05 19:52:56) (main.py:250)
2025-08-05 19:53:19,427 [INFO] main -   - memoo2707: Bekleniyor (2025-08-05 19:52:52) (main.py:250)
2025-08-05 19:53:19,427 [INFO] main -   - ismailislami2: Bekleniyor (2025-08-05 19:52:46) (main.py:250)
2025-08-05 19:53:19,427 [INFO] main -   - vindotrxgoe: Bekleniyor (2025-08-05 19:52:41) (main.py:250)
2025-08-05 19:53:19,427 [INFO] main -   - levo190344: Bekleniyor (2025-08-05 19:52:38) (main.py:250)
2025-08-05 19:53:19,427 [INFO] main -   - bibiseydicem: Bekleniyor (2025-08-05 19:52:34) (main.py:250)
2025-08-05 19:53:19,427 [INFO] main -   - ismail_.goren76: Bekleniyor (2025-08-05 19:52:28) (main.py:250)
2025-08-05 19:53:19,427 [INFO] main -   - kalisdir5: Bekleniyor (2025-08-05 19:52:23) (main.py:250)
2025-08-05 19:53:19,427 [INFO] main -   - mehmetalierbilofficial: Bekleniyor (2025-08-05 19:52:19) (main.py:250)
2025-08-05 19:53:19,427 [INFO] main - 2️⃣ Status Checker başlatılıyor (13 kullanıcı) (main.py:254)
2025-08-05 19:53:19,427 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:373)
2025-08-05 19:53:19,427 [INFO] main - 🔧 Phase lock alınıyor... (main.py:379)
2025-08-05 19:53:19,427 [INFO] main - 🔧 Phase lock alındı (main.py:381)
2025-08-05 19:53:19,427 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:389)
2025-08-05 19:53:19,427 [INFO] main - 2️⃣ Status Checker başlatılıyor (13 kullanıcı) (main.py:392)
2025-08-05 19:53:19,442 [INFO] main - 📋 Kullanıcılar: ['mehmetalierbilofficial', 'kalisdir5', 'ismail_.goren76', 'bibiseydicem', 'levo190344', 'vindotrxgoe', 'ismailislami2', 'memoo2707', 'yldz.edal', 'kod_3545', '25yagz.efe25', 'seiiarsslan', 'pubgi.hatun'] (main.py:393)
2025-08-05 19:53:19,442 [INFO] main - 📋 Publishers formatı: [{'username': 'mehmetalierbilofficial'}, {'username': 'kalisdir5'}, {'username': 'ismail_.goren76'}, {'username': 'bibiseydicem'}, {'username': 'levo190344'}, {'username': 'vindotrxgoe'}, {'username': 'ismailislami2'}, {'username': 'memoo2707'}, {'username': 'yldz.edal'}, {'username': 'kod_3545'}, {'username': '25yagz.efe25'}, {'username': 'seiiarsslan'}, {'username': 'pubgi.hatun'}] (main.py:397)
2025-08-05 19:53:19,442 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:399)
2025-08-05 19:53:19,442 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:409)
2025-08-05 19:53:19,442 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:410)
2025-08-05 19:53:19,442 [INFO] main - 🔧 Thread daemon: True (main.py:411)
2025-08-05 19:53:19,442 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:420)
2025-08-05 19:53:19,442 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-08-05 19:53:19,442 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:424)
2025-08-05 19:53:19,442 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 13 (status_checker.py:84)
2025-08-05 19:53:19,442 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:433)
2025-08-05 19:53:19,442 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 9468 (status_checker.py:84)
2025-08-05 19:53:19,442 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-08-05 19:53:19,442 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:53:19,442 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-08-05 19:53:19,442 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-08-05 19:53:22,599 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-08-05 19:53:22,599 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 13 kullanıcı işlenecek (status_checker.py:84)
2025-08-05 19:53:22,599 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'mehmetalierbilofficial'}, {'username': 'kalisdir5'}, {'username': 'ismail_.goren76'}] (status_checker.py:84)
2025-08-05 19:53:22,599 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-08-05 19:53:22,599 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-08-05 19:53:22,599 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-08-05 19:53:22,599 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-08-05 19:53:22,599 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-08-05 19:53:22,599 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-08-05 19:53:22,599 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-08-05 19:53:22,599 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-08-05 19:53:24,258 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:53:27,273 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-08-05 19:53:27,273 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:53:27,273 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-08-05 19:53:27,273 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-08-05 19:53:31,841 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-08-05 19:53:31,845 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-08-05 19:53:32,089 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-08-05 19:53:32,089 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-08-05 19:53:32,104 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-08-05 19:53:32,104 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-08-05 19:53:35,337 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-08-05 19:53:35,337 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 13 kullanıcı). (status_checker.py:84)
2025-08-05 19:53:38,168 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-08-05 19:53:43,282 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 13 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-08-05 19:53:46,467 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-08-05 19:53:49,044 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 13 (status_checker.py:84)
2025-08-05 19:53:49,049 [INFO] StatusChecker - [STATUS_CHECKER] 💾 13 kaydın durumu güncellendi. (status_checker.py:84)
2025-08-05 19:53:51,763 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-08-05 19:53:56,181 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-08-05 19:53:56,306 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-08-05 19:53:56,306 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-08-05 19:53:56,306 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:271)
2025-08-05 19:53:56,306 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:274)
2025-08-05 19:53:56,306 [INFO] main - ✅ Status Checker thread temizlendi (callback) (main.py:277)
2025-08-05 19:53:56,306 [INFO] main - 3️⃣ Message Sender başlatılıyor (10 kullanıcı) (main.py:289)
2025-08-05 19:53:56,321 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:454)
2025-08-05 19:53:56,321 [INFO] main - ✅ Thread temizlendi, scraper başlatılıyor (main.py:458)
2025-08-05 19:53:57,337 [INFO] main - 🔧 start_scraper çağrıldı (main.py:325)
2025-08-05 19:53:57,337 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:327)
2025-08-05 19:53:57,337 [INFO] main - 🔧 Phase lock alındı (main.py:330)
2025-08-05 19:53:57,337 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:347)
2025-08-05 19:53:57,337 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:350)
2025-08-05 19:53:57,337 [INFO] main - ✅ Scraper thread başlatıldı (main.py:364)
2025-08-05 19:54:01,406 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-08-05 19:54:01,409 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-08-05 19:54:10,695 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-08-05 19:54:14,244 [INFO] scraper_thread - [SCRAPER] mehmetalierbilofficial | 913 (scraper_thread.py:321)
2025-08-05 19:54:24,384 [INFO] scraper_thread - [SCRAPER] akkurt096 | 1 (scraper_thread.py:321)
2025-08-05 19:54:28,409 [INFO] scraper_thread - [SCRAPER] muhammedcetinkaya35 | 0 (scraper_thread.py:321)
2025-08-05 19:54:33,982 [INFO] scraper_thread - [SCRAPER] dortkardesler_ | 367 (scraper_thread.py:321)
2025-08-05 19:54:38,301 [INFO] scraper_thread - [SCRAPER] rafetgngr0626 | 11 (scraper_thread.py:321)
2025-08-05 19:54:43,687 [INFO] scraper_thread - [SCRAPER] baran_gelis_12 | 0 (scraper_thread.py:321)
2025-08-05 19:54:47,720 [INFO] scraper_thread - [SCRAPER] 44malatyam34 | 5 (scraper_thread.py:321)
2025-08-05 19:54:51,663 [INFO] scraper_thread - [SCRAPER] joker_bey3356 | 12 (scraper_thread.py:321)
2025-08-05 19:54:56,971 [INFO] scraper_thread - [SCRAPER] volkanercan27 | 4 (scraper_thread.py:321)
2025-08-05 19:55:00,822 [INFO] scraper_thread - [SCRAPER] valeriosa2 | 38 (scraper_thread.py:321)
2025-08-05 19:55:05,162 [INFO] scraper_thread - [SCRAPER] obiramedli21.04 | 2 (scraper_thread.py:321)
2025-08-05 19:55:10,757 [INFO] scraper_thread - [SCRAPER] yavuzwlvrbe | 2 (scraper_thread.py:321)
2025-08-05 19:55:10,762 [INFO] scraper_thread - [SCRAPER] Süre doldu (1.0 dk) (scraper_thread.py:321)
2025-08-05 19:55:10,763 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 12 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-08-05 19:55:13,271 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-08-05 19:55:13,271 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:225)
2025-08-05 19:55:13,271 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:228)
2025-08-05 19:55:13,271 [INFO] main - ✅ Scraper thread temizlendi (callback) (main.py:231)
2025-08-05 19:55:13,271 [INFO] main - 🔍 Son 1 dakikada toplam 12 kullanıcı: (main.py:248)
2025-08-05 19:55:13,271 [INFO] main -   - yavuzwlvrbe: Bekleniyor (2025-08-05 19:55:10) (main.py:250)
2025-08-05 19:55:13,271 [INFO] main -   - obiramedli21.04: Bekleniyor (2025-08-05 19:55:05) (main.py:250)
2025-08-05 19:55:13,271 [INFO] main -   - valeriosa2: Bekleniyor (2025-08-05 19:55:00) (main.py:250)
2025-08-05 19:55:13,271 [INFO] main -   - volkanercan27: Bekleniyor (2025-08-05 19:54:56) (main.py:250)
2025-08-05 19:55:13,271 [INFO] main -   - joker_bey3356: Bekleniyor (2025-08-05 19:54:51) (main.py:250)
2025-08-05 19:55:13,271 [INFO] main -   - 44malatyam34: Bekleniyor (2025-08-05 19:54:47) (main.py:250)
2025-08-05 19:55:13,271 [INFO] main -   - baran_gelis_12: Bekleniyor (2025-08-05 19:54:43) (main.py:250)
2025-08-05 19:55:13,271 [INFO] main -   - rafetgngr0626: Bekleniyor (2025-08-05 19:54:38) (main.py:250)
2025-08-05 19:55:13,271 [INFO] main -   - dortkardesler_: Bekleniyor (2025-08-05 19:54:33) (main.py:250)
2025-08-05 19:55:13,271 [INFO] main -   - muhammedcetinkaya35: Bekleniyor (2025-08-05 19:54:28) (main.py:250)
2025-08-05 19:55:13,271 [INFO] main -   - akkurt096: Bekleniyor (2025-08-05 19:54:24) (main.py:250)
2025-08-05 19:55:13,271 [INFO] main -   - mehmetalierbilofficial: Bekleniyor (2025-08-05 19:54:14) (main.py:250)
2025-08-05 19:55:13,271 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:254)
2025-08-05 19:55:13,271 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:373)
2025-08-05 19:55:13,271 [INFO] main - 🔧 Phase lock alınıyor... (main.py:379)
2025-08-05 19:55:13,271 [INFO] main - 🔧 Phase lock alındı (main.py:381)
2025-08-05 19:55:13,271 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:389)
2025-08-05 19:55:13,271 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:392)
2025-08-05 19:55:13,271 [INFO] main - 📋 Kullanıcılar: ['mehmetalierbilofficial', 'akkurt096', 'muhammedcetinkaya35', 'dortkardesler_', 'rafetgngr0626', 'baran_gelis_12', '44malatyam34', 'joker_bey3356', 'volkanercan27', 'valeriosa2', 'obiramedli21.04', 'yavuzwlvrbe'] (main.py:393)
2025-08-05 19:55:13,271 [INFO] main - 📋 Publishers formatı: [{'username': 'mehmetalierbilofficial'}, {'username': 'akkurt096'}, {'username': 'muhammedcetinkaya35'}, {'username': 'dortkardesler_'}, {'username': 'rafetgngr0626'}, {'username': 'baran_gelis_12'}, {'username': '44malatyam34'}, {'username': 'joker_bey3356'}, {'username': 'volkanercan27'}, {'username': 'valeriosa2'}, {'username': 'obiramedli21.04'}, {'username': 'yavuzwlvrbe'}] (main.py:397)
2025-08-05 19:55:13,271 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:399)
2025-08-05 19:55:13,271 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:409)
2025-08-05 19:55:13,271 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:410)
2025-08-05 19:55:13,286 [INFO] main - 🔧 Thread daemon: True (main.py:411)
2025-08-05 19:55:13,286 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:420)
2025-08-05 19:55:13,286 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-08-05 19:55:13,286 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:424)
2025-08-05 19:55:13,286 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 12 (status_checker.py:84)
2025-08-05 19:55:13,286 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:433)
2025-08-05 19:55:13,286 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 2992 (status_checker.py:84)
2025-08-05 19:55:13,286 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-08-05 19:55:13,286 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:55:13,286 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-08-05 19:55:13,286 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-08-05 19:55:16,458 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-08-05 19:55:16,458 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı işlenecek (status_checker.py:84)
2025-08-05 19:55:16,458 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'mehmetalierbilofficial'}, {'username': 'akkurt096'}, {'username': 'muhammedcetinkaya35'}] (status_checker.py:84)
2025-08-05 19:55:16,458 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-08-05 19:55:16,458 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-08-05 19:55:16,458 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-08-05 19:55:16,458 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-08-05 19:55:16,458 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-08-05 19:55:16,458 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-08-05 19:55:16,458 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-08-05 19:55:16,458 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-08-05 19:55:18,215 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:55:21,223 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-08-05 19:55:21,223 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:55:21,223 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-08-05 19:55:21,223 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-08-05 19:55:26,141 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-08-05 19:55:26,148 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-08-05 19:55:26,382 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-08-05 19:55:26,389 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-08-05 19:55:26,390 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-08-05 19:55:26,391 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-08-05 19:55:29,612 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-08-05 19:55:29,612 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 12 kullanıcı). (status_checker.py:84)
2025-08-05 19:55:32,926 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-08-05 19:55:38,305 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-08-05 19:55:41,680 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-08-05 19:55:43,771 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 12 (status_checker.py:84)
2025-08-05 19:55:43,771 [INFO] StatusChecker - [STATUS_CHECKER] 💾 12 kaydın durumu güncellendi. (status_checker.py:84)
2025-08-05 19:55:47,242 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-08-05 19:55:51,633 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-08-05 19:55:51,774 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-08-05 19:55:51,774 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-08-05 19:55:51,774 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:271)
2025-08-05 19:55:51,774 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:274)
2025-08-05 19:55:51,774 [INFO] main - ✅ Status Checker thread temizlendi (callback) (main.py:277)
2025-08-05 19:55:51,774 [INFO] main - 3️⃣ Message Sender başlatılıyor (8 kullanıcı) (main.py:289)
2025-08-05 19:55:51,774 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:454)
2025-08-05 19:55:51,774 [INFO] main - ✅ Thread temizlendi, scraper başlatılıyor (main.py:458)
2025-08-05 19:55:52,789 [INFO] main - 🔧 start_scraper çağrıldı (main.py:325)
2025-08-05 19:55:52,789 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:327)
2025-08-05 19:55:52,789 [INFO] main - 🔧 Phase lock alındı (main.py:330)
2025-08-05 19:55:52,789 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:347)
2025-08-05 19:55:52,789 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:350)
2025-08-05 19:55:52,789 [INFO] main - ✅ Scraper thread başlatıldı (main.py:364)
2025-08-05 19:55:56,755 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-08-05 19:55:56,755 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-08-05 19:56:00,076 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-08-05 19:56:03,403 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-08-05 19:56:09,152 [INFO] scraper_thread - [SCRAPER] mehmetalierbilofficial | 750 (scraper_thread.py:321)
2025-08-05 19:56:15,222 [INFO] scraper_thread - [SCRAPER] sergiyi4 | 4 (scraper_thread.py:321)
2025-08-05 19:56:20,822 [INFO] scraper_thread - [SCRAPER] izollu_tekin | 6 (scraper_thread.py:321)
2025-08-05 19:56:25,338 [INFO] scraper_thread - [SCRAPER] busem3258 | 8 (scraper_thread.py:321)
2025-08-05 19:56:29,853 [INFO] scraper_thread - [SCRAPER] hermessefa1_ | 26 (scraper_thread.py:321)
2025-08-05 19:56:30,311 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-08-05 19:56:34,119 [INFO] scraper_thread - [SCRAPER] abdulkadir.iti | 3 (scraper_thread.py:321)
2025-08-05 19:56:38,089 [INFO] scraper_thread - [SCRAPER] tiktoknays1 | 4 (scraper_thread.py:321)
2025-08-05 19:56:43,333 [INFO] scraper_thread - [SCRAPER] starfamesfashion | 334 (scraper_thread.py:321)
2025-08-05 19:56:48,681 [INFO] scraper_thread - [SCRAPER] cilginmikrofonn | 109 (scraper_thread.py:321)
2025-08-05 19:56:52,685 [INFO] scraper_thread - [SCRAPER] rockyxmain | 73 (scraper_thread.py:321)
2025-08-05 19:56:58,162 [INFO] scraper_thread - [SCRAPER] sedanury.1 | 3 (scraper_thread.py:321)
2025-08-05 19:57:00,482 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-08-05 19:57:03,652 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 11 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-08-05 19:57:06,230 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-08-05 19:57:06,230 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:225)
2025-08-05 19:57:06,230 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:228)
2025-08-05 19:57:06,230 [INFO] main - ✅ Scraper thread temizlendi (callback) (main.py:231)
2025-08-05 19:57:06,230 [INFO] main - 🔍 Son 1 dakikada toplam 11 kullanıcı: (main.py:248)
2025-08-05 19:57:06,230 [INFO] main -   - sedanury.1: Bekleniyor (2025-08-05 19:56:58) (main.py:250)
2025-08-05 19:57:06,230 [INFO] main -   - rockyxmain: Bekleniyor (2025-08-05 19:56:52) (main.py:250)
2025-08-05 19:57:06,230 [INFO] main -   - cilginmikrofonn: Bekleniyor (2025-08-05 19:56:48) (main.py:250)
2025-08-05 19:57:06,230 [INFO] main -   - starfamesfashion: Bekleniyor (2025-08-05 19:56:43) (main.py:250)
2025-08-05 19:57:06,230 [INFO] main -   - tiktoknays1: Bekleniyor (2025-08-05 19:56:38) (main.py:250)
2025-08-05 19:57:06,230 [INFO] main -   - abdulkadir.iti: Bekleniyor (2025-08-05 19:56:34) (main.py:250)
2025-08-05 19:57:06,230 [INFO] main -   - hermessefa1_: Bekleniyor (2025-08-05 19:56:29) (main.py:250)
2025-08-05 19:57:06,230 [INFO] main -   - busem3258: Bekleniyor (2025-08-05 19:56:25) (main.py:250)
2025-08-05 19:57:06,230 [INFO] main -   - izollu_tekin: Bekleniyor (2025-08-05 19:56:20) (main.py:250)
2025-08-05 19:57:06,230 [INFO] main -   - sergiyi4: Bekleniyor (2025-08-05 19:56:15) (main.py:250)
2025-08-05 19:57:06,230 [INFO] main -   - mehmetalierbilofficial: Bekleniyor (2025-08-05 19:56:09) (main.py:250)
2025-08-05 19:57:06,230 [INFO] main - 2️⃣ Status Checker başlatılıyor (11 kullanıcı) (main.py:254)
2025-08-05 19:57:06,245 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:373)
2025-08-05 19:57:06,245 [INFO] main - 🔧 Phase lock alınıyor... (main.py:379)
2025-08-05 19:57:06,245 [INFO] main - 🔧 Phase lock alındı (main.py:381)
2025-08-05 19:57:06,245 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:389)
2025-08-05 19:57:06,245 [INFO] main - 2️⃣ Status Checker başlatılıyor (11 kullanıcı) (main.py:392)
2025-08-05 19:57:06,245 [INFO] main - 📋 Kullanıcılar: ['mehmetalierbilofficial', 'sergiyi4', 'izollu_tekin', 'busem3258', 'hermessefa1_', 'abdulkadir.iti', 'tiktoknays1', 'starfamesfashion', 'cilginmikrofonn', 'rockyxmain', 'sedanury.1'] (main.py:393)
2025-08-05 19:57:06,245 [INFO] main - 📋 Publishers formatı: [{'username': 'mehmetalierbilofficial'}, {'username': 'sergiyi4'}, {'username': 'izollu_tekin'}, {'username': 'busem3258'}, {'username': 'hermessefa1_'}, {'username': 'abdulkadir.iti'}, {'username': 'tiktoknays1'}, {'username': 'starfamesfashion'}, {'username': 'cilginmikrofonn'}, {'username': 'rockyxmain'}, {'username': 'sedanury.1'}] (main.py:397)
2025-08-05 19:57:06,245 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:399)
2025-08-05 19:57:06,261 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:409)
2025-08-05 19:57:06,261 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:410)
2025-08-05 19:57:06,261 [INFO] main - 🔧 Thread daemon: True (main.py:411)
2025-08-05 19:57:06,261 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:420)
2025-08-05 19:57:06,277 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-08-05 19:57:06,277 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:424)
2025-08-05 19:57:06,277 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 11 (status_checker.py:84)
2025-08-05 19:57:06,277 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:433)
2025-08-05 19:57:06,277 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 2344 (status_checker.py:84)
2025-08-05 19:57:06,277 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-08-05 19:57:06,277 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:57:06,277 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-08-05 19:57:06,277 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-08-05 19:57:09,464 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-08-05 19:57:09,464 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 11 kullanıcı işlenecek (status_checker.py:84)
2025-08-05 19:57:09,464 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'mehmetalierbilofficial'}, {'username': 'sergiyi4'}, {'username': 'izollu_tekin'}] (status_checker.py:84)
2025-08-05 19:57:09,464 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-08-05 19:57:09,464 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-08-05 19:57:09,464 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-08-05 19:57:09,464 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-08-05 19:57:09,464 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-08-05 19:57:09,464 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-08-05 19:57:09,464 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-08-05 19:57:09,464 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-08-05 19:57:10,904 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:57:13,907 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-08-05 19:57:13,907 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:57:13,907 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-08-05 19:57:13,907 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-08-05 19:57:18,663 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-08-05 19:57:18,691 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-08-05 19:57:18,877 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-08-05 19:57:18,884 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-08-05 19:57:18,884 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-08-05 19:57:18,885 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-08-05 19:57:22,035 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-08-05 19:57:22,035 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 11 kullanıcı). (status_checker.py:84)
2025-08-05 19:57:24,876 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-08-05 19:57:30,053 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 11 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-08-05 19:57:30,725 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-08-05 19:57:33,882 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-08-05 19:57:35,430 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 11 (status_checker.py:84)
2025-08-05 19:57:35,435 [INFO] StatusChecker - [STATUS_CHECKER] 💾 11 kaydın durumu güncellendi. (status_checker.py:84)
2025-08-05 19:57:39,058 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-08-05 19:57:43,475 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-08-05 19:57:43,600 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-08-05 19:57:43,600 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-08-05 19:57:43,600 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:271)
2025-08-05 19:57:43,600 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:274)
2025-08-05 19:57:43,615 [INFO] main - ✅ Status Checker thread temizlendi (callback) (main.py:277)
2025-08-05 19:57:43,615 [INFO] main - 3️⃣ Message Sender başlatılıyor (6 kullanıcı) (main.py:289)
2025-08-05 19:57:43,615 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:454)
2025-08-05 19:57:43,615 [INFO] main - ✅ Thread temizlendi, scraper başlatılıyor (main.py:458)
2025-08-05 19:57:44,631 [INFO] main - 🔧 start_scraper çağrıldı (main.py:325)
2025-08-05 19:57:44,631 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:327)
2025-08-05 19:57:44,631 [INFO] main - 🔧 Phase lock alındı (main.py:330)
2025-08-05 19:57:44,631 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:347)
2025-08-05 19:57:44,631 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:350)
2025-08-05 19:57:44,631 [INFO] main - ✅ Scraper thread başlatıldı (main.py:364)
2025-08-05 19:57:48,707 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-08-05 19:57:48,709 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-08-05 19:57:55,228 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-08-05 19:58:00,912 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-08-05 19:58:01,449 [INFO] scraper_thread - [SCRAPER] mehmetalierbilofficial | 785 (scraper_thread.py:321)
2025-08-05 19:58:05,603 [INFO] scraper_thread - [SCRAPER] levo190344 | 1 (scraper_thread.py:321)
2025-08-05 19:58:10,632 [INFO] scraper_thread - [SCRAPER] thebest.6383 | 2 (scraper_thread.py:321)
2025-08-05 19:58:14,408 [INFO] scraper_thread - [SCRAPER] asettocorsaroll | 3 (scraper_thread.py:321)
2025-08-05 19:58:19,254 [INFO] scraper_thread - [SCRAPER] yldz.edal | 294 (scraper_thread.py:321)
2025-08-05 19:58:24,839 [INFO] scraper_thread - [SCRAPER] yakamoz_4.6.4.0 | 1 (scraper_thread.py:321)
2025-08-05 19:58:29,493 [INFO] scraper_thread - [SCRAPER] halit.dagasan | 4 (scraper_thread.py:321)
2025-08-05 19:58:34,213 [INFO] scraper_thread - [SCRAPER] 66mustafa660yk | 1 (scraper_thread.py:321)
2025-08-05 19:58:38,989 [INFO] scraper_thread - [SCRAPER] 25yagz.efe25 | 2 (scraper_thread.py:321)
2025-08-05 19:58:43,308 [INFO] scraper_thread - [SCRAPER] asil_selma | 20 (scraper_thread.py:321)
2025-08-05 19:58:47,978 [INFO] scraper_thread - [SCRAPER] ecrinimmm011 | 3 (scraper_thread.py:321)
2025-08-05 19:58:51,864 [INFO] scraper_thread - [SCRAPER] aleme__inat | 3 (scraper_thread.py:321)
2025-08-05 19:58:55,669 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 12 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-08-05 19:58:58,205 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-08-05 19:58:58,205 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:225)
2025-08-05 19:58:58,205 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:228)
2025-08-05 19:58:58,205 [INFO] main - ✅ Scraper thread temizlendi (callback) (main.py:231)
2025-08-05 19:58:58,205 [INFO] main - 🔍 Son 1 dakikada toplam 12 kullanıcı: (main.py:248)
2025-08-05 19:58:58,205 [INFO] main -   - aleme__inat: Bekleniyor (2025-08-05 19:58:51) (main.py:250)
2025-08-05 19:58:58,205 [INFO] main -   - ecrinimmm011: Bekleniyor (2025-08-05 19:58:47) (main.py:250)
2025-08-05 19:58:58,205 [INFO] main -   - asil_selma: Bekleniyor (2025-08-05 19:58:43) (main.py:250)
2025-08-05 19:58:58,205 [INFO] main -   - 25yagz.efe25: Bekleniyor (2025-08-05 19:58:38) (main.py:250)
2025-08-05 19:58:58,205 [INFO] main -   - 66mustafa660yk: Bekleniyor (2025-08-05 19:58:34) (main.py:250)
2025-08-05 19:58:58,205 [INFO] main -   - halit.dagasan: Bekleniyor (2025-08-05 19:58:29) (main.py:250)
2025-08-05 19:58:58,205 [INFO] main -   - yakamoz_4.6.4.0: Bekleniyor (2025-08-05 19:58:24) (main.py:250)
2025-08-05 19:58:58,205 [INFO] main -   - yldz.edal: Bekleniyor (2025-08-05 19:58:19) (main.py:250)
2025-08-05 19:58:58,205 [INFO] main -   - asettocorsaroll: Bekleniyor (2025-08-05 19:58:14) (main.py:250)
2025-08-05 19:58:58,205 [INFO] main -   - thebest.6383: Bekleniyor (2025-08-05 19:58:10) (main.py:250)
2025-08-05 19:58:58,205 [INFO] main -   - levo190344: Bekleniyor (2025-08-05 19:58:05) (main.py:250)
2025-08-05 19:58:58,205 [INFO] main -   - mehmetalierbilofficial: Bekleniyor (2025-08-05 19:58:01) (main.py:250)
2025-08-05 19:58:58,205 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:254)
2025-08-05 19:58:58,205 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:373)
2025-08-05 19:58:58,205 [INFO] main - 🔧 Phase lock alınıyor... (main.py:379)
2025-08-05 19:58:58,205 [INFO] main - 🔧 Phase lock alındı (main.py:381)
2025-08-05 19:58:58,205 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:389)
2025-08-05 19:58:58,205 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:392)
2025-08-05 19:58:58,205 [INFO] main - 📋 Kullanıcılar: ['mehmetalierbilofficial', 'levo190344', 'thebest.6383', 'asettocorsaroll', 'yldz.edal', 'yakamoz_4.6.4.0', 'halit.dagasan', '66mustafa660yk', '25yagz.efe25', 'asil_selma', 'ecrinimmm011', 'aleme__inat'] (main.py:393)
2025-08-05 19:58:58,205 [INFO] main - 📋 Publishers formatı: [{'username': 'mehmetalierbilofficial'}, {'username': 'levo190344'}, {'username': 'thebest.6383'}, {'username': 'asettocorsaroll'}, {'username': 'yldz.edal'}, {'username': 'yakamoz_4.6.4.0'}, {'username': 'halit.dagasan'}, {'username': '66mustafa660yk'}, {'username': '25yagz.efe25'}, {'username': 'asil_selma'}, {'username': 'ecrinimmm011'}, {'username': 'aleme__inat'}] (main.py:397)
2025-08-05 19:58:58,205 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:399)
2025-08-05 19:58:58,205 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:409)
2025-08-05 19:58:58,221 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:410)
2025-08-05 19:58:58,221 [INFO] main - 🔧 Thread daemon: True (main.py:411)
2025-08-05 19:58:58,221 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:420)
2025-08-05 19:58:58,221 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-08-05 19:58:58,221 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:424)
2025-08-05 19:58:58,221 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 12 (status_checker.py:84)
2025-08-05 19:58:58,221 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:433)
2025-08-05 19:58:58,221 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 5660 (status_checker.py:84)
2025-08-05 19:58:58,221 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-08-05 19:58:58,221 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:58:58,221 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-08-05 19:58:58,221 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-08-05 19:59:01,393 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-08-05 19:59:01,393 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı işlenecek (status_checker.py:84)
2025-08-05 19:59:01,393 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'mehmetalierbilofficial'}, {'username': 'levo190344'}, {'username': 'thebest.6383'}] (status_checker.py:84)
2025-08-05 19:59:01,393 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-08-05 19:59:01,393 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-08-05 19:59:01,393 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-08-05 19:59:01,393 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-08-05 19:59:01,393 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-08-05 19:59:01,393 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-08-05 19:59:01,393 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-08-05 19:59:01,393 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-08-05 19:59:02,674 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:59:05,690 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-08-05 19:59:05,690 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-08-05 19:59:05,690 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-08-05 19:59:05,690 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-08-05 19:59:10,675 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-08-05 19:59:10,675 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-08-05 19:59:10,819 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-08-05 19:59:10,826 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-08-05 19:59:10,826 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-08-05 19:59:10,827 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-08-05 19:59:14,189 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-08-05 19:59:14,189 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 12 kullanıcı). (status_checker.py:84)
2025-08-05 19:59:16,643 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-08-05 19:59:22,033 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-08-05 19:59:25,144 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-08-05 19:59:27,214 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 12 (status_checker.py:84)
2025-08-05 19:59:27,214 [INFO] StatusChecker - [STATUS_CHECKER] 💾 12 kaydın durumu güncellendi. (status_checker.py:84)
2025-08-05 19:59:30,603 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-08-05 19:59:35,973 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-08-05 19:59:36,098 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-08-05 19:59:36,098 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-08-05 19:59:36,098 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:271)
2025-08-05 19:59:36,098 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:274)
2025-08-05 19:59:36,098 [INFO] main - ✅ Status Checker thread temizlendi (callback) (main.py:277)
2025-08-05 19:59:36,114 [INFO] main - 3️⃣ Message Sender başlatılıyor (10 kullanıcı) (main.py:289)
2025-08-05 19:59:36,114 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:454)
2025-08-05 19:59:36,114 [INFO] main - ✅ Thread temizlendi, scraper başlatılıyor (main.py:458)
2025-08-05 19:59:37,130 [INFO] main - 🔧 start_scraper çağrıldı (main.py:325)
2025-08-05 19:59:37,130 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:327)
2025-08-05 19:59:37,130 [INFO] main - 🔧 Phase lock alındı (main.py:330)
2025-08-05 19:59:37,130 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:347)
2025-08-05 19:59:37,130 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:350)
2025-08-05 19:59:37,130 [INFO] main - ✅ Scraper thread başlatıldı (main.py:364)
2025-08-05 19:59:41,278 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-08-05 19:59:41,278 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-08-05 19:59:47,845 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-08-05 19:59:53,317 [INFO] scraper_thread - [SCRAPER] mehmetalierbilofficial | 728 (scraper_thread.py:321)
2025-08-05 19:59:57,805 [INFO] scraper_thread - [SCRAPER] cardakcagkebap | 1900 (scraper_thread.py:321)
2025-08-05 20:00:02,642 [INFO] scraper_thread - [SCRAPER] nalan_3536 | 21 (scraper_thread.py:321)
2025-08-05 20:00:07,147 [INFO] scraper_thread - [SCRAPER] 8kadirpubg | 22 (scraper_thread.py:321)
2025-08-05 20:00:11,066 [INFO] scraper_thread - [SCRAPER] mahsundeniz0445 | 3 (scraper_thread.py:321)
2025-08-05 20:00:15,751 [INFO] scraper_thread - [SCRAPER] valeriosa2 | 35 (scraper_thread.py:321)
2025-08-05 20:00:19,709 [INFO] scraper_thread - [SCRAPER] ortaclarceyizevi | 17 (scraper_thread.py:321)
2025-08-05 20:00:23,707 [WARNING] main - ⚠️ scraper thread 1200 saniyedir yanıt vermiyor, yeniden başlatılıyor... (main.py:168)
2025-08-05 20:00:23,709 [INFO] main - 🔄 Timeout nedeniyle scraper yeniden başlatılıyor (main.py:185)
2025-08-05 20:00:24,325 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 7 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-08-05 20:00:26,872 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-08-05 20:00:26,872 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:225)
2025-08-05 20:00:26,872 [INFO] main - 🔧 Thread temizleniyor (callback) (main.py:228)
2025-08-05 20:00:26,872 [INFO] main - ✅ Scraper thread temizlendi (callback) (main.py:231)
2025-08-05 20:00:26,872 [INFO] main - 🔍 Son 1 dakikada toplam 18 kullanıcı: (main.py:248)
2025-08-05 20:00:26,872 [INFO] main -   - ortaclarceyizevi: Bekleniyor (2025-08-05 20:00:19) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main -   - valeriosa2: Bekleniyor (2025-08-05 20:00:15) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main -   - mahsundeniz0445: Bekleniyor (2025-08-05 20:00:11) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main -   - 8kadirpubg: Bekleniyor (2025-08-05 20:00:07) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main -   - nalan_3536: Bekleniyor (2025-08-05 20:00:02) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main -   - cardakcagkebap: Bekleniyor (2025-08-05 19:59:57) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main -   - mehmetalierbilofficial: Bekleniyor (2025-08-05 19:59:53) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main -   - ecrinimmm011: Diğer nedenler (2025-08-05 19:59:27) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main -   - asil_selma: Uygun (2025-08-05 19:59:27) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main -   - 66mustafa660yk: Uygun (2025-08-05 19:59:27) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main -   - halit.dagasan: Uygun (2025-08-05 19:59:27) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main -   - yakamoz_4.6.4.0: Uygun (2025-08-05 19:59:27) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main -   - asettocorsaroll: Uygun (2025-08-05 19:59:27) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main -   - thebest.6383: Uygun (2025-08-05 19:59:27) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main -   - 25yagz.efe25: Uygun (2025-08-05 19:59:27) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main -   - levo190344: Uygun (2025-08-05 19:59:27) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main -   - aleme__inat: Uygun (2025-08-05 19:59:27) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main -   - yldz.edal: Uygun (2025-08-05 19:59:27) (main.py:250)
2025-08-05 20:00:26,872 [INFO] main - 2️⃣ Status Checker başlatılıyor (7 kullanıcı) (main.py:254)
2025-08-05 20:00:26,872 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:373)
2025-08-05 20:00:26,872 [INFO] main - 🔧 Phase lock alınıyor... (main.py:379)
2025-08-05 20:00:28,716 [INFO] main - 🔧 start_scraper çağrıldı (main.py:325)
2025-08-05 20:00:28,716 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:327)
2025-08-06 09:34:39,483 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-08-06 09:34:39,498 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-08-06 09:34:39,498 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:207)
2025-08-06 09:34:39,498 [INFO] main - ✅ Otomasyon durduruldu (main.py:216)
2025-08-06 09:34:39,623 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
