2025-05-16 20:56:15,354 [INFO] __main__ - Döngü süresi 1 dakika olarak ayarlandı (main.py:54)
2025-05-16 20:56:15,354 [INFO] __main__ - Headless modu: False (main.py:58)
2025-05-16 20:56:15,354 [INFO] __main__ - <PERSON><PERSON><PERSON><PERSON> başlatıldı. Döngü süresi: 1 dakika. Headless: False (main.py:65)
2025-05-16 20:56:15,354 [INFO] __main__ - Thread monitö<PERSON><PERSON> ba<PERSON>ıldı (main.py:85)
2025-05-16 20:56:15,354 [INFO] __main__ - Thread monitör<PERSON> başlatıldı (main.py:222)
2025-05-16 20:56:15,354 [INFO] __main__ - [AŞAMA 1/3] SCRAPER BAŞLATILIYOR - 2025-05-16 20:56:15.354275 (main.py:254)
2025-05-16 20:56:15,354 [INFO] __main__ - Chrome işlemleri temizleniyor... (main.py:257)
2025-05-16 20:56:15,354 [INFO] __main__ - Chrome işlemleri sonlandırılıyor... (utils.py:164)
2025-05-16 20:56:17,682 [INFO] __main__ - Chrome işlemleri başarıyla sonlandırıldı (utils.py:258)
2025-05-16 20:56:19,682 [INFO] __main__ - Scraper sinyal bağlantısı kuruldu (main.py:291)
2025-05-16 20:56:19,682 [INFO] __main__ - Scraper thread başlatıldı ve çalışıyor... (main.py:298)
2025-05-16 20:56:19,682 [INFO] scraper_thread - Chrome işlemleri sonlandırılıyor... (scraper_thread.py:257)
2025-05-16 20:56:21,979 [INFO] scraper_thread - Chrome işlemleri başarıyla sonlandırıldı (scraper_thread.py:257)
2025-05-16 20:56:27,713 [INFO] scraper_thread - TikTok Live sayfasına gidiliyor... (scraper_thread.py:257)
2025-05-16 20:56:38,327 [INFO] scraper_thread - Scraper başladı, süre: 1 dk (scraper_thread.py:257)
2025-05-16 20:56:42,687 [INFO] scraper_thread - pircioglulive | 1500 (scraper_thread.py:257)
2025-05-16 20:56:42,702 [INFO] scraper_thread - ✅ pircioglulive veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:257)
2025-05-16 20:56:55,266 [INFO] scraper_thread - usercv4ct7a5mj | 2 (scraper_thread.py:257)
2025-05-16 20:56:55,270 [INFO] scraper_thread - ✅ usercv4ct7a5mj veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:257)
2025-05-16 20:57:01,489 [INFO] scraper_thread - dollkiaa | 0 (scraper_thread.py:257)
2025-05-16 20:57:01,497 [INFO] scraper_thread - ✅ dollkiaa veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:257)
2025-05-16 20:57:06,481 [INFO] scraper_thread - onahev | 43 (scraper_thread.py:257)
2025-05-16 20:57:06,484 [INFO] scraper_thread - ✅ onahev veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:257)
2025-05-16 20:57:11,866 [INFO] scraper_thread - krissmay18 | 0 (scraper_thread.py:257)
2025-05-16 20:57:11,869 [INFO] scraper_thread - ✅ krissmay18 veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:257)
2025-05-16 20:57:16,344 [INFO] scraper_thread - honeyhoney_123 | 2 (scraper_thread.py:257)
2025-05-16 20:57:16,349 [INFO] scraper_thread - ✅ honeyhoney_123 veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:257)
2025-05-16 20:57:22,234 [INFO] scraper_thread - malaikkhan23 | 10 (scraper_thread.py:257)
2025-05-16 20:57:22,238 [INFO] scraper_thread - ✅ malaikkhan23 veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:257)
2025-05-16 20:57:28,666 [INFO] scraper_thread - viktoqgvm0i | 4 (scraper_thread.py:257)
2025-05-16 20:57:28,669 [INFO] scraper_thread - ✅ viktoqgvm0i veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:257)
2025-05-16 20:57:33,834 [INFO] scraper_thread - ivy_elizabeth94 | 3 (scraper_thread.py:257)
2025-05-16 20:57:33,838 [INFO] scraper_thread - ✅ ivy_elizabeth94 veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:257)
2025-05-16 20:57:38,509 [INFO] scraper_thread - Toplam 9 benzersiz kullanıcı bulundu (scraper_thread.py:257)
2025-05-16 20:57:45,797 [INFO] __main__ - !!! MONİTÖR: Scraper thread tamamlandı !!! (main.py:91)
2025-05-16 20:57:45,797 [INFO] __main__ - MONİTÖR: active_thread None olarak ayarlandı (main.py:95)
2025-05-16 20:57:45,797 [INFO] __main__ - MONİTÖR: Veritabanından alınan son 1 dakikadaki kullanıcı sayısı: 8 (main.py:112)
2025-05-16 20:57:45,797 [INFO] __main__ - MONİTÖR: Örnek kayıtlar: [{'username': 'ivy_elizabeth94', 'timestamp': datetime.datetime(2025, 5, 16, 20, 57, 33), 'status': 'Bekleniyor'}, {'username': 'viktoqgvm0i', 'timestamp': datetime.datetime(2025, 5, 16, 20, 57, 28), 'status': 'Bekleniyor'}, {'username': 'malaikkhan23', 'timestamp': datetime.datetime(2025, 5, 16, 20, 57, 22), 'status': 'Bekleniyor'}] (main.py:113)
2025-05-16 20:57:45,797 [INFO] __main__ - MONİTÖR: İşlenecek kullanıcılar: ['ivy_elizabeth94', 'viktoqgvm0i', 'malaikkhan23', 'honeyhoney_123', 'krissmay18', 'onahev', 'dollkiaa', 'usercv4ct7a5mj'] (main.py:121)
2025-05-16 20:57:45,797 [INFO] __main__ - MONİTÖR: Chrome işlemleri temizleniyor... (main.py:124)
2025-05-16 20:57:45,797 [INFO] __main__ - Chrome işlemleri sonlandırılıyor... (utils.py:164)
2025-05-16 20:57:48,110 [INFO] __main__ - Chrome işlemleri başarıyla sonlandırıldı (utils.py:258)
2025-05-16 20:57:51,125 [INFO] __main__ - MONİTÖR: Status checker için 8 kullanıcı hazırlandı (main.py:135)
2025-05-16 20:57:51,125 [INFO] __main__ - [AŞAMA 2/3] STATUS CHECKER BAŞLATILIYOR - 2025-05-16 20:57:51.125618 (main.py:324)
2025-05-16 20:57:51,125 [INFO] __main__ - Status checker thread başlatıldı ve çalışıyor... (main.py:355)
2025-05-16 20:57:51,125 [INFO] StatusChecker - 🔍 Chrome StatusChecker başlıyor... (status_checker.py:87)
2025-05-16 20:57:51,125 [INFO] StatusChecker - Diğer Chrome işlemlerinin kapanması bekleniyor... (status_checker.py:87)
2025-05-16 20:57:51,125 [INFO] StatusChecker - status_checker diğer thread'lerin bitmesini bekliyor... (status_checker.py:87)
2025-05-16 20:57:51,125 [INFO] StatusChecker - Tüm diğer thread'ler tamamlandı, status_checker başlayabilir. (status_checker.py:87)
2025-05-16 20:57:51,125 [INFO] StatusChecker - Chrome WebDriver kurulumu başlıyor... (status_checker.py:87)
2025-05-16 20:57:51,125 [INFO] StatusChecker - Kullanılacak ChromeDriver yolu: chromedriver.exe (status_checker.py:87)
2025-05-16 20:57:51,125 [INFO] StatusChecker - Kullanılacak Chrome profil ana yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:87)
2025-05-16 20:57:51,125 [INFO] StatusChecker - Kullanılacak Chrome profil klasör adı: Profile 1 (status_checker.py:87)
2025-05-16 20:57:51,125 [INFO] StatusChecker - Kullanılacak Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe (status_checker.py:87)
2025-05-16 20:57:51,125 [INFO] StatusChecker - Chrome tercihleri ayarlandı. ChromeDriver servisi başlatılıyor... (status_checker.py:87)
2025-05-16 20:57:53,082 [INFO] StatusChecker - ✅ Chrome WebDriver başarıyla başlatıldı. (status_checker.py:87)
2025-05-16 20:57:53,177 [INFO] StatusChecker - JavaScript ile navigator.webdriver maskelendi. (status_checker.py:87)
2025-05-16 20:57:53,177 [INFO] StatusChecker - 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:87)
2025-05-16 20:57:53,180 [INFO] StatusChecker - ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:87)
2025-05-16 20:57:56,279 [INFO] StatusChecker - Sayfa yüklenmesi için 15 saniye bekleniyor... (status_checker.py:87)
2025-05-16 20:58:11,769 [INFO] StatusChecker - ✅ Sayfa başarıyla yüklendi: https://live-backstage.tiktok.com/portal/overview (status_checker.py:87)
2025-05-16 20:58:14,897 [INFO] StatusChecker - 📦 Chunk 1/1 (içinde 8 kullanıcı). (status_checker.py:87)
2025-05-16 20:58:18,644 [INFO] StatusChecker - ✅ Panel açıldı (davet butonu). (status_checker.py:87)
2025-05-16 20:58:26,154 [INFO] StatusChecker - ✅ 8 kullanıcı textarea'ya yazıldı. (status_checker.py:87)
2025-05-16 20:58:29,804 [INFO] StatusChecker - ✅ 'İleri' butonuna tıklandı. (status_checker.py:87)
2025-05-16 20:58:30,825 [INFO] StatusChecker - 🔍 Toplam kullanıcı sayısı: 8 (status_checker.py:87)
2025-05-16 20:58:30,825 [INFO] StatusChecker - 💾 8 kaydın durumu güncellendi. (status_checker.py:87)
2025-05-16 20:58:34,553 [INFO] StatusChecker - ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:87)
2025-05-16 20:58:39,026 [INFO] StatusChecker - Chrome WebDriver kapatıldı (status_checker.py:87)
2025-05-16 20:58:39,026 [INFO] StatusChecker - StatusChecker tamamlandı, ana programa sinyal gönderildi. (status_checker.py:87)
2025-05-16 20:58:39,932 [INFO] __main__ - !!! MONİTÖR: Status checker thread tamamlandı !!! (main.py:143)
2025-05-16 20:58:39,932 [INFO] __main__ - MONİTÖR: active_thread None olarak ayarlandı (main.py:147)
2025-05-16 20:58:39,932 [INFO] __main__ - MONİTÖR: Chrome işlemleri temizleniyor... (main.py:150)
2025-05-16 20:58:39,932 [INFO] __main__ - Chrome işlemleri sonlandırılıyor... (utils.py:164)
2025-05-16 20:58:42,229 [INFO] __main__ - Chrome işlemleri başarıyla sonlandırıldı (utils.py:258)
2025-05-16 20:58:45,245 [INFO] __main__ - MONİTÖR: Uygun kullanıcılar kontrol ediliyor... (main.py:161)
2025-05-16 20:58:45,588 [INFO] __main__ - MONİTÖR: 9282 uygun kullanıcı bulundu, message sender başlatılıyor... (main.py:171)
2025-05-16 20:58:45,604 [INFO] __main__ - MONİTÖR: Message sender başlatılıyor... (main.py:172)
2025-05-16 20:58:45,604 [INFO] __main__ - [AŞAMA 3/3] MESSAGE SENDER BAŞLATILIYOR - 2025-05-16 20:58:45.604406 (main.py:380)
2025-05-16 20:58:45,620 [INFO] __main__ - Message sender için 77 adet kullanıcı bulundu (Uygun/Uygun Elite) (main.py:411)
2025-05-16 20:58:45,620 [INFO] __main__ - Message sender thread başlatıldı (main.py:444)
2025-05-16 20:58:45,620 [INFO] MessageSender - 📩 MessageSender başlatıldı. (message_sender_thread.py:369)
2025-05-16 20:58:45,729 [INFO] MessageSender - ✅ Önceki Chrome işlemleri kapatıldı. (message_sender_thread.py:369)
2025-05-16 20:58:45,729 [INFO] MessageSender - ✅ Chrome yolu doğrulandı (message_sender_thread.py:369)
2025-05-16 20:58:45,729 [INFO] MessageSender - ✅ Chrome binary yolu ayarlandı: C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe (message_sender_thread.py:369)
2025-05-16 20:58:45,729 [INFO] MessageSender - ✅ Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data, Profil: Profile 1 (message_sender_thread.py:369)
2025-05-16 20:58:45,729 [INFO] MessageSender - ✅ ChromeDriver başlatılıyor: C:\chromedriver.exe (message_sender_thread.py:369)
2025-05-16 20:59:09,031 [INFO] MessageSender - [allah_bana_yeter_55] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,032 [INFO] MessageSender - [semihmumaay] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,034 [INFO] MessageSender - [cagla1sanlier] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,035 [INFO] MessageSender - [usercv4ct7a5mj] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,036 [INFO] MessageSender - [rabissss06_66] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,037 [INFO] MessageSender - [anneauxsaints] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,038 [INFO] MessageSender - [ayseaynur40] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,039 [INFO] MessageSender - [firathan_] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,040 [INFO] MessageSender - [_necla] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,041 [INFO] MessageSender - [recepuruk] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,042 [INFO] MessageSender - [ibrahim_akbas2163] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,043 [INFO] MessageSender - [pubg63mobile6] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,044 [INFO] MessageSender - [zhredemirel07] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,045 [INFO] MessageSender - [serapakgl4] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,048 [INFO] MessageSender - [babykadin] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,049 [INFO] MessageSender - [sedat.tiken] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,050 [INFO] MessageSender - [bylutfucan] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,051 [INFO] MessageSender - [burcuguzel57] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,052 [INFO] MessageSender - [p3player1] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,053 [INFO] MessageSender - [yorgun_zaman4] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,054 [INFO] MessageSender - [nil0723] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,054 [INFO] MessageSender - [veysel343461] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,055 [INFO] MessageSender - [furkan774y] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,056 [INFO] MessageSender - [papatyakokusumisali.0] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,057 [INFO] MessageSender - [karamsarbiri7] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,059 [INFO] MessageSender - [yilmaz_savascilariz01] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,059 [INFO] MessageSender - [ahmet.elik584] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,060 [INFO] MessageSender - [boranipekkk] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,061 [INFO] MessageSender - [22y56] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,064 [INFO] MessageSender - [serginho_01.07] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,065 [INFO] MessageSender - [szl.mehmet] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,067 [INFO] MessageSender - [adampcgamer] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,067 [INFO] MessageSender - [sevcan_cano] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,068 [INFO] MessageSender - [aslancap] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,069 [INFO] MessageSender - [hasen.187] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,070 [INFO] MessageSender - [samety1907] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,071 [INFO] MessageSender - [sefa.apkn] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,072 [INFO] MessageSender - [2121_aslan] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,073 [INFO] MessageSender - [sohbetturkmen1310] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,074 [INFO] MessageSender - [melisakittyworld] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,075 [INFO] MessageSender - [wx.crew] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,076 [INFO] MessageSender - [mahmutamac] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,077 [INFO] MessageSender - [nazmi.cerez] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,080 [INFO] MessageSender - [x_dogan34] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,081 [INFO] MessageSender - [erkankuscu4] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,082 [INFO] MessageSender - [505j55] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,083 [INFO] MessageSender - [artis474] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,084 [INFO] MessageSender - [tahir_da_01] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,085 [INFO] MessageSender - [iremimmm09] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,086 [INFO] MessageSender - [ogulcanuzum] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,087 [INFO] MessageSender - [mehri1558] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,088 [INFO] MessageSender - [yetmedimi44] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,089 [INFO] MessageSender - [hasananl04] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,090 [INFO] MessageSender - [seymosss10] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,090 [INFO] MessageSender - [ysruyasuuu] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,092 [INFO] MessageSender - [esad.gebic10] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,092 [INFO] MessageSender - [tacettin.aydas] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,096 [INFO] MessageSender - [beeeyzzza] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,097 [INFO] MessageSender - [humeyrakcmn] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,098 [INFO] MessageSender - [yyusufbulut] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,099 [INFO] MessageSender - [niilssu] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,100 [INFO] MessageSender - [user4756893138] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,100 [INFO] MessageSender - [r.rayn4a] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,102 [INFO] MessageSender - [eboliiil] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,103 [INFO] MessageSender - [ramo.82.33] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,103 [INFO] MessageSender - [saricivcivimmm] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,104 [INFO] MessageSender - [26esmeryy] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,105 [INFO] MessageSender - [ball.01_01] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,106 [INFO] MessageSender - [eyllloztrkk] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,107 [INFO] MessageSender - [hd19881] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,108 [INFO] MessageSender - [kahvefali_48] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,109 [INFO] MessageSender - [kriptoata1] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,112 [INFO] MessageSender - [rab1aaksu_] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,113 [INFO] MessageSender - [lider3321] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,114 [INFO] MessageSender - [gold.ercan.27_34] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,115 [INFO] MessageSender - [bekirdurna2524] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,116 [INFO] MessageSender - [ali_eyim6382] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 20:59:09,116 [INFO] MessageSender - 📩 Tüm mesajlar başarıyla gönderildi, yeni döngü başlatılıyor... (message_sender_thread.py:369)
2025-05-16 20:59:12,756 [INFO] MessageSender - Chrome driver kapatıldı. (message_sender_thread.py:369)
2025-05-16 20:59:16,991 [INFO] __main__ - !!! MONİTÖR: Message sender thread tamamlandı !!! (main.py:184)
2025-05-16 20:59:16,991 [INFO] __main__ - MONİTÖR: active_thread None olarak ayarlandı (main.py:188)
2025-05-16 20:59:16,991 [INFO] __main__ - MONİTÖR: Chrome işlemleri temizleniyor... (main.py:191)
2025-05-16 20:59:16,991 [INFO] __main__ - Chrome işlemleri sonlandırılıyor... (utils.py:164)
2025-05-16 20:59:19,288 [INFO] __main__ - Chrome işlemleri başarıyla sonlandırıldı (utils.py:258)
2025-05-16 20:59:22,303 [INFO] __main__ - MONİTÖR: Tam döngü tamamlandı, scraper'ı tekrar başlatıyorum (main.py:202)
2025-05-16 20:59:22,303 [INFO] __main__ - [AŞAMA 1/3] SCRAPER BAŞLATILIYOR - 2025-05-16 20:59:22.303653 (main.py:254)
2025-05-16 20:59:22,303 [INFO] __main__ - Chrome işlemleri temizleniyor... (main.py:257)
2025-05-16 20:59:22,303 [INFO] __main__ - Chrome işlemleri sonlandırılıyor... (utils.py:164)
2025-05-16 20:59:24,600 [INFO] __main__ - Chrome işlemleri başarıyla sonlandırıldı (utils.py:258)
2025-05-16 20:59:26,616 [INFO] __main__ - Önceki scraper thread temizlendi (main.py:270)
2025-05-16 20:59:26,616 [INFO] __main__ - Scraper sinyal bağlantısı kuruldu (main.py:291)
2025-05-16 20:59:26,616 [INFO] __main__ - Scraper thread başlatıldı ve çalışıyor... (main.py:298)
2025-05-16 20:59:26,616 [INFO] scraper_thread - Chrome işlemleri sonlandırılıyor... (scraper_thread.py:257)
2025-05-16 20:59:28,913 [INFO] scraper_thread - Chrome işlemleri başarıyla sonlandırıldı (scraper_thread.py:257)
2025-05-16 20:59:31,904 [INFO] scraper_thread - TikTok Live sayfasına gidiliyor... (scraper_thread.py:257)
2025-05-16 20:59:38,585 [INFO] scraper_thread - Scraper başladı, süre: 1 dk (scraper_thread.py:257)
2025-05-16 20:59:45,352 [INFO] scraper_thread - pircioglulive | 1900 (scraper_thread.py:257)
2025-05-16 20:59:45,356 [INFO] scraper_thread - ✅ pircioglulive veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:257)
2025-05-16 20:59:50,834 [INFO] scraper_thread - ameriqcek | 4 (scraper_thread.py:257)
2025-05-16 20:59:50,857 [INFO] scraper_thread - ✅ ameriqcek veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:257)
2025-05-16 20:59:56,769 [INFO] scraper_thread - kenziiqq | 0 (scraper_thread.py:257)
2025-05-16 20:59:56,776 [INFO] scraper_thread - ✅ kenziiqq veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:257)
2025-05-16 21:00:02,176 [INFO] scraper_thread - saba_khan605 | 55 (scraper_thread.py:257)
2025-05-16 21:00:02,179 [INFO] scraper_thread - ✅ saba_khan605 veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:257)
2025-05-16 21:00:06,724 [INFO] scraper_thread - jass__.18 | 54 (scraper_thread.py:257)
2025-05-16 21:00:06,728 [INFO] scraper_thread - ✅ jass__.18 veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:257)
2025-05-16 21:00:11,093 [INFO] scraper_thread - tanyafenton40 | 2 (scraper_thread.py:257)
2025-05-16 21:00:13,582 [INFO] scraper_thread - ✅ tanyafenton40 veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:257)
2025-05-16 21:00:14,467 [INFO] __main__ - Otomasyon durdurma sinyali gönderildi (main.py:229)
2025-05-16 21:00:14,470 [INFO] __main__ - Scraper thread durduruldu (main.py:234)
2025-05-16 21:00:14,472 [INFO] StatusChecker - ⏹ StatusChecker durdurma sinyali alındı. (status_checker.py:87)
2025-05-16 21:00:14,473 [INFO] __main__ - Status checker thread durduruldu (main.py:238)
2025-05-16 21:00:14,474 [INFO] MessageSender - ⏹ MessageSender durdurma sinyali alındı. (message_sender_thread.py:369)
2025-05-16 21:00:14,474 [INFO] __main__ - Message sender thread durduruldu (main.py:242)
2025-05-16 21:00:14,474 [INFO] __main__ - Tüm thread'ler durduruldu (main.py:244)
2025-05-16 21:09:53,744 [INFO] __main__ - Döngü süresi 1 dakika olarak ayarlandı (main.py:54)
2025-05-16 21:09:53,744 [INFO] __main__ - Headless modu: False (main.py:58)
2025-05-16 21:09:53,744 [INFO] __main__ - Otomasyon başlatıldı. Döngü süresi: 1 dakika. Headless: False (main.py:65)
2025-05-16 21:09:53,744 [INFO] __main__ - Thread monitörü başlatıldı (main.py:85)
2025-05-16 21:09:53,744 [INFO] __main__ - Thread monitörü başlatıldı (main.py:222)
2025-05-16 21:09:53,744 [INFO] __main__ - [AŞAMA 1/3] SCRAPER BAŞLATILIYOR - 2025-05-16 21:09:53.744900 (main.py:254)
2025-05-16 21:09:53,744 [INFO] __main__ - Chrome işlemleri temizleniyor... (main.py:257)
2025-05-16 21:09:53,744 [INFO] __main__ - Chrome işlemleri sonlandırılıyor... (utils.py:164)
2025-05-16 21:09:56,104 [INFO] __main__ - Chrome işlemleri başarıyla sonlandırıldı (utils.py:258)
2025-05-16 21:09:58,104 [INFO] __main__ - Scraper sinyal bağlantısı kuruldu (main.py:291)
2025-05-16 21:09:58,104 [INFO] __main__ - Scraper thread başlatıldı ve çalışıyor... (main.py:298)
2025-05-16 21:09:58,104 [INFO] scraper_thread - Chrome işlemleri sonlandırılıyor... (scraper_thread.py:257)
2025-05-16 21:10:01,791 [INFO] scraper_thread - Chrome işlemleri başarıyla sonlandırıldı (scraper_thread.py:257)
2025-05-16 21:10:29,917 [INFO] scraper_thread - TikTok Live sayfasına gidiliyor... (scraper_thread.py:257)
2025-05-16 21:10:59,274 [INFO] scraper_thread - Scraper başladı, süre: 1 dk (scraper_thread.py:257)
2025-05-16 21:12:46,577 [INFO] scraper_thread - Toplam 0 benzersiz kullanıcı bulundu (scraper_thread.py:257)
2025-05-16 21:12:49,369 [INFO] __main__ - Otomasyon durdurma sinyali gönderildi (main.py:229)
2025-05-16 21:12:49,369 [INFO] __main__ - Scraper thread durduruldu (main.py:234)
2025-05-16 21:12:49,369 [INFO] __main__ - Tüm thread'ler durduruldu (main.py:244)
2025-05-16 21:15:15,307 [INFO] __main__ - Döngü süresi 1 dakika olarak ayarlandı (main.py:54)
2025-05-16 21:15:15,307 [INFO] __main__ - Headless modu: False (main.py:58)
2025-05-16 21:15:15,307 [INFO] __main__ - Otomasyon başlatıldı. Döngü süresi: 1 dakika. Headless: False (main.py:65)
2025-05-16 21:15:15,307 [INFO] __main__ - Thread monitörü başlatıldı (main.py:85)
2025-05-16 21:15:15,307 [INFO] __main__ - Thread monitörü başlatıldı (main.py:201)
2025-05-16 21:15:15,307 [INFO] __main__ - [AŞAMA 1/3] SCRAPER BAŞLATILIYOR - 2025-05-16 21:15:15.307391 (main.py:233)
2025-05-16 21:15:15,307 [INFO] __main__ - Scraper sinyal bağlantısı kuruldu (main.py:265)
2025-05-16 21:15:15,307 [INFO] __main__ - Scraper thread başlatıldı ve çalışıyor... (main.py:272)
2025-05-16 21:15:15,307 [INFO] scraper_thread - Chrome işlemleri temizleniyor... (scraper_thread.py:261)
2025-05-16 21:15:15,307 [INFO] scraper_thread - Chrome işlemleri sonlandırılıyor... (scraper_thread.py:261)
2025-05-16 21:15:17,698 [INFO] scraper_thread - Chrome işlemleri başarıyla sonlandırıldı (scraper_thread.py:261)
2025-05-16 21:15:21,277 [INFO] scraper_thread - TikTok Live sayfasına gidiliyor... (scraper_thread.py:261)
2025-05-16 21:15:28,235 [INFO] scraper_thread - Scraper başladı, süre: 1 dk (scraper_thread.py:261)
2025-05-16 21:15:32,938 [INFO] scraper_thread - pircioglulive | 2100 (scraper_thread.py:261)
2025-05-16 21:15:32,950 [INFO] scraper_thread - ✅ pircioglulive veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:261)
2025-05-16 21:15:46,142 [INFO] scraper_thread - gulsimayy6 | 11 (scraper_thread.py:261)
2025-05-16 21:15:46,169 [INFO] scraper_thread - ✅ gulsimayy6 veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:261)
2025-05-16 21:15:46,399 [INFO] __main__ - Otomasyon durdurma sinyali gönderildi (main.py:208)
2025-05-16 21:15:46,427 [INFO] __main__ - Scraper thread durduruldu (main.py:213)
2025-05-16 21:15:46,439 [INFO] __main__ - Tüm thread'ler durduruldu (main.py:223)
2025-05-16 22:27:35,323 [INFO] __main__ - Döngü süresi 1 dakika olarak ayarlandı (main.py:54)
2025-05-16 22:27:35,323 [INFO] __main__ - Headless modu: False (main.py:58)
2025-05-16 22:27:35,323 [INFO] __main__ - Thread monitörü başlatıldı (main.py:199)
2025-05-16 22:27:35,323 [INFO] scraper_thread - Chrome işlemleri temizleniyor... (scraper_thread.py:261)
2025-05-16 22:27:35,323 [INFO] scraper_thread - Chrome işlemleri sonlandırılıyor... (scraper_thread.py:261)
2025-05-16 22:27:37,682 [INFO] scraper_thread - Chrome işlemleri başarıyla sonlandırıldı (scraper_thread.py:261)
2025-05-16 22:27:41,591 [INFO] scraper_thread - TikTok Live sayfasına gidiliyor... (scraper_thread.py:261)
2025-05-16 22:27:49,428 [INFO] scraper_thread - Scraper başladı, süre: 1 dk (scraper_thread.py:261)
2025-05-16 22:28:03,294 [INFO] scraper_thread - nazsalastroloji1 | 9 (scraper_thread.py:261)
2025-05-16 22:28:03,315 [INFO] scraper_thread - ✅ nazsalastroloji1 veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:261)
2025-05-16 22:28:09,417 [INFO] scraper_thread - edaacanimmm0619 | 4 (scraper_thread.py:261)
2025-05-16 22:28:09,420 [INFO] scraper_thread - ✅ edaacanimmm0619 veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:261)
2025-05-16 22:28:14,753 [INFO] scraper_thread - bendogus | 13 (scraper_thread.py:261)
2025-05-16 22:28:14,757 [INFO] scraper_thread - ✅ bendogus veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:261)
2025-05-16 22:28:19,166 [INFO] scraper_thread - haffsaa111 | 1 (scraper_thread.py:261)
2025-05-16 22:28:19,169 [INFO] scraper_thread - ✅ haffsaa111 veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:261)
2025-05-16 22:28:22,994 [INFO] scraper_thread - 01.zehirr | 32 (scraper_thread.py:261)
2025-05-16 22:28:28,855 [INFO] scraper_thread - ✅ 01.zehirr veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:261)
2025-05-16 22:28:34,608 [INFO] scraper_thread - tucekarakaya | 9 (scraper_thread.py:261)
2025-05-16 22:28:34,614 [INFO] scraper_thread - ✅ tucekarakaya veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:261)
2025-05-16 22:28:39,946 [INFO] scraper_thread - numtarotholistichealing | 7 (scraper_thread.py:261)
2025-05-16 22:28:39,949 [INFO] scraper_thread - ✅ numtarotholistichealing veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:261)
2025-05-16 22:28:45,509 [INFO] scraper_thread - mertpolatoglu0 | 67 (scraper_thread.py:261)
2025-05-16 22:28:45,514 [INFO] scraper_thread - ✅ mertpolatoglu0 veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:261)
2025-05-16 22:28:49,440 [INFO] scraper_thread - palasse3 | 2 (scraper_thread.py:261)
2025-05-16 22:28:49,442 [INFO] scraper_thread - ✅ palasse3 veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:261)
2025-05-16 22:28:49,442 [INFO] scraper_thread - Süre doldu (1 dk) (scraper_thread.py:261)
2025-05-16 22:28:49,443 [INFO] scraper_thread - Toplam 9 benzersiz kullanıcı bulundu (scraper_thread.py:261)
2025-05-16 22:28:57,307 [INFO] __main__ - !!! MONİTÖR: Scraper thread tamamlandı !!! (main.py:89)
2025-05-16 22:28:57,307 [INFO] __main__ - MONİTÖR: active_thread None olarak ayarlandı (main.py:93)
2025-05-16 22:28:57,307 [INFO] __main__ - MONİTÖR: Veritabanından alınan son 1 dakikadaki kullanıcı sayısı: 9 (main.py:110)
2025-05-16 22:28:57,307 [INFO] __main__ - MONİTÖR: Örnek kayıtlar: [{'username': 'palasse3', 'timestamp': datetime.datetime(2025, 5, 16, 22, 28, 49), 'status': 'Bekleniyor'}, {'username': 'mertpolatoglu0', 'timestamp': datetime.datetime(2025, 5, 16, 22, 28, 45), 'status': 'Bekleniyor'}, {'username': 'numtarotholistichealing', 'timestamp': datetime.datetime(2025, 5, 16, 22, 28, 39), 'status': 'Bekleniyor'}] (main.py:111)
2025-05-16 22:28:57,307 [INFO] __main__ - MONİTÖR: İşlenecek kullanıcılar: ['palasse3', 'mertpolatoglu0', 'numtarotholistichealing', 'tucekarakaya', '01.zehirr', 'haffsaa111', 'bendogus', 'edaacanimmm0619', 'nazsalastroloji1'] (main.py:119)
2025-05-16 22:29:00,323 [INFO] __main__ - MONİTÖR: Status checker için 9 kullanıcı hazırlandı (main.py:126)
2025-05-16 22:29:00,323 [INFO] __main__ - [AŞAMA 2/3] STATUS CHECKER BAŞLATILIYOR - 2025-05-16 22:29:00.323014 (main.py:292)
2025-05-16 22:29:00,323 [INFO] __main__ - Status checker thread başlatıldı ve çalışıyor... (main.py:323)
2025-05-16 22:29:00,323 [INFO] StatusChecker - 🔍 Chrome StatusChecker başlıyor... (status_checker.py:87)
2025-05-16 22:29:00,323 [INFO] StatusChecker - Diğer Chrome işlemlerinin kapanması bekleniyor... (status_checker.py:87)
2025-05-16 22:29:00,323 [INFO] StatusChecker - status_checker diğer thread'lerin bitmesini bekliyor... (status_checker.py:87)
2025-05-16 22:29:00,323 [INFO] StatusChecker - Tüm diğer thread'ler tamamlandı, status_checker başlayabilir. (status_checker.py:87)
2025-05-16 22:29:00,323 [INFO] StatusChecker - Chrome WebDriver kurulumu başlıyor... (status_checker.py:87)
2025-05-16 22:29:00,323 [INFO] StatusChecker - Kullanılacak ChromeDriver yolu: chromedriver.exe (status_checker.py:87)
2025-05-16 22:29:00,323 [INFO] StatusChecker - Kullanılacak Chrome profil ana yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:87)
2025-05-16 22:29:00,323 [INFO] StatusChecker - Kullanılacak Chrome profil klasör adı: Profile 1 (status_checker.py:87)
2025-05-16 22:29:00,323 [INFO] StatusChecker - Kullanılacak Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe (status_checker.py:87)
2025-05-16 22:29:00,323 [INFO] StatusChecker - Chrome tercihleri ayarlandı. ChromeDriver servisi başlatılıyor... (status_checker.py:87)
2025-05-16 22:29:02,294 [INFO] StatusChecker - ✅ Chrome WebDriver başarıyla başlatıldı. (status_checker.py:87)
2025-05-16 22:29:02,349 [INFO] StatusChecker - JavaScript ile navigator.webdriver maskelendi. (status_checker.py:87)
2025-05-16 22:29:02,351 [INFO] StatusChecker - 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:87)
2025-05-16 22:29:02,353 [INFO] StatusChecker - ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:87)
2025-05-16 22:29:04,724 [INFO] StatusChecker - Sayfa yüklenmesi için 15 saniye bekleniyor... (status_checker.py:87)
2025-05-16 22:29:20,145 [INFO] StatusChecker - ✅ Sayfa başarıyla yüklendi: https://live-backstage.tiktok.com/portal/overview (status_checker.py:87)
2025-05-16 22:29:23,277 [INFO] StatusChecker - 📦 Chunk 1/1 (içinde 9 kullanıcı). (status_checker.py:87)
2025-05-16 22:29:26,923 [INFO] StatusChecker - ✅ Panel açıldı (davet butonu). (status_checker.py:87)
2025-05-16 22:29:34,598 [INFO] StatusChecker - ✅ 9 kullanıcı textarea'ya yazıldı. (status_checker.py:87)
2025-05-16 22:29:37,682 [INFO] StatusChecker - ✅ 'İleri' butonuna tıklandı. (status_checker.py:87)
2025-05-16 22:29:39,870 [INFO] StatusChecker - 🔍 Toplam kullanıcı sayısı: 9 (status_checker.py:87)
2025-05-16 22:29:39,870 [INFO] StatusChecker - 💾 9 kaydın durumu güncellendi. (status_checker.py:87)
2025-05-16 22:29:43,104 [INFO] StatusChecker - ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:87)
2025-05-16 22:29:48,635 [INFO] StatusChecker - Chrome WebDriver kapatıldı (status_checker.py:87)
2025-05-16 22:29:48,635 [INFO] StatusChecker - StatusChecker tamamlandı, ana programa sinyal gönderildi. (status_checker.py:87)
2025-05-16 22:29:49,307 [INFO] __main__ - !!! MONİTÖR: Status checker thread tamamlandı !!! (main.py:134)
2025-05-16 22:29:49,307 [INFO] __main__ - MONİTÖR: active_thread None olarak ayarlandı (main.py:138)
2025-05-16 22:29:52,323 [INFO] __main__ - MONİTÖR: Uygun kullanıcılar kontrol ediliyor... (main.py:145)
2025-05-16 22:30:08,901 [INFO] __main__ - MONİTÖR: 9284 uygun kullanıcı bulundu, message sender başlatılıyor... (main.py:155)
2025-05-16 22:30:08,901 [INFO] __main__ - MONİTÖR: Message sender başlatılıyor... (main.py:156)
2025-05-16 22:30:08,901 [INFO] __main__ - [AŞAMA 3/3] MESSAGE SENDER BAŞLATILIYOR - 2025-05-16 22:30:08.901137 (main.py:348)
2025-05-16 22:30:08,916 [INFO] __main__ - Message sender için 79 adet kullanıcı bulundu (Uygun/Uygun Elite) (main.py:379)
2025-05-16 22:30:08,916 [INFO] __main__ - Message sender thread başlatıldı (main.py:412)
2025-05-16 22:30:08,916 [INFO] MessageSender - 📩 MessageSender başlatıldı. (message_sender_thread.py:369)
2025-05-16 22:30:09,010 [INFO] MessageSender - ✅ Önceki Chrome işlemleri kapatıldı. (message_sender_thread.py:369)
2025-05-16 22:30:09,010 [INFO] MessageSender - ✅ Chrome yolu doğrulandı (message_sender_thread.py:369)
2025-05-16 22:30:09,010 [INFO] MessageSender - ✅ Chrome binary yolu ayarlandı: C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe (message_sender_thread.py:369)
2025-05-16 22:30:09,010 [INFO] MessageSender - ✅ Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data, Profil: Profile 1 (message_sender_thread.py:369)
2025-05-16 22:30:09,010 [INFO] MessageSender - ✅ ChromeDriver başlatılıyor: C:\chromedriver.exe (message_sender_thread.py:369)
2025-05-16 22:30:37,597 [INFO] MessageSender - [allah_bana_yeter_55] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,598 [INFO] MessageSender - [semihmumaay] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,598 [INFO] MessageSender - [cagla1sanlier] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,599 [INFO] MessageSender - [palasse3] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,600 [INFO] MessageSender - [haffsaa111] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,601 [INFO] MessageSender - [usercv4ct7a5mj] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,602 [INFO] MessageSender - [rabissss06_66] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,603 [INFO] MessageSender - [anneauxsaints] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,604 [INFO] MessageSender - [ayseaynur40] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,605 [INFO] MessageSender - [firathan_] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,606 [INFO] MessageSender - [_necla] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,607 [INFO] MessageSender - [recepuruk] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,608 [INFO] MessageSender - [ibrahim_akbas2163] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,609 [INFO] MessageSender - [pubg63mobile6] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,610 [INFO] MessageSender - [zhredemirel07] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,611 [INFO] MessageSender - [serapakgl4] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,614 [INFO] MessageSender - [babykadin] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,615 [INFO] MessageSender - [sedat.tiken] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,616 [INFO] MessageSender - [bylutfucan] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,617 [INFO] MessageSender - [burcuguzel57] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,617 [INFO] MessageSender - [p3player1] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,618 [INFO] MessageSender - [yorgun_zaman4] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,619 [INFO] MessageSender - [nil0723] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,620 [INFO] MessageSender - [veysel343461] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,620 [INFO] MessageSender - [furkan774y] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,621 [INFO] MessageSender - [papatyakokusumisali.0] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,622 [INFO] MessageSender - [karamsarbiri7] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,623 [INFO] MessageSender - [yilmaz_savascilariz01] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,624 [INFO] MessageSender - [ahmet.elik584] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,625 [INFO] MessageSender - [boranipekkk] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,626 [INFO] MessageSender - [22y56] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,629 [INFO] MessageSender - [serginho_01.07] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,630 [INFO] MessageSender - [szl.mehmet] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,631 [INFO] MessageSender - [adampcgamer] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,631 [INFO] MessageSender - [sevcan_cano] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,632 [INFO] MessageSender - [aslancap] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,633 [INFO] MessageSender - [hasen.187] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,634 [INFO] MessageSender - [samety1907] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,635 [INFO] MessageSender - [sefa.apkn] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,636 [INFO] MessageSender - [2121_aslan] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,637 [INFO] MessageSender - [sohbetturkmen1310] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,638 [INFO] MessageSender - [melisakittyworld] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,639 [INFO] MessageSender - [wx.crew] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,640 [INFO] MessageSender - [mahmutamac] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,641 [INFO] MessageSender - [nazmi.cerez] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,642 [INFO] MessageSender - [x_dogan34] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,645 [INFO] MessageSender - [erkankuscu4] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,646 [INFO] MessageSender - [505j55] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,647 [INFO] MessageSender - [artis474] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,648 [INFO] MessageSender - [tahir_da_01] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,649 [INFO] MessageSender - [iremimmm09] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,650 [INFO] MessageSender - [ogulcanuzum] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,651 [INFO] MessageSender - [mehri1558] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,656 [INFO] MessageSender - [yetmedimi44] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,658 [INFO] MessageSender - [hasananl04] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,659 [INFO] MessageSender - [seymosss10] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,661 [INFO] MessageSender - [ysruyasuuu] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,662 [INFO] MessageSender - [esad.gebic10] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,666 [INFO] MessageSender - [tacettin.aydas] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,667 [INFO] MessageSender - [beeeyzzza] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,668 [INFO] MessageSender - [humeyrakcmn] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,669 [INFO] MessageSender - [yyusufbulut] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,670 [INFO] MessageSender - [niilssu] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,672 [INFO] MessageSender - [user4756893138] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,673 [INFO] MessageSender - [r.rayn4a] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,674 [INFO] MessageSender - [eboliiil] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,675 [INFO] MessageSender - [ramo.82.33] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,676 [INFO] MessageSender - [saricivcivimmm] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,677 [INFO] MessageSender - [26esmeryy] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,677 [INFO] MessageSender - [ball.01_01] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,678 [INFO] MessageSender - [eyllloztrkk] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,680 [INFO] MessageSender - [hd19881] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,681 [INFO] MessageSender - [kahvefali_48] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,683 [INFO] MessageSender - [kriptoata1] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,684 [INFO] MessageSender - [rab1aaksu_] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,689 [INFO] MessageSender - [lider3321] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,691 [INFO] MessageSender - [gold.ercan.27_34] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,693 [INFO] MessageSender - [bekirdurna2524] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,694 [INFO] MessageSender - [ali_eyim6382] hata: argument of type 'NoneType' is not iterable (message_sender_thread.py:369)
2025-05-16 22:30:37,699 [INFO] MessageSender - 📩 Tüm mesajlar başarıyla gönderildi, yeni döngü başlatılıyor... (message_sender_thread.py:369)
2025-05-16 22:30:40,354 [INFO] MessageSender - Chrome driver kapatıldı. (message_sender_thread.py:369)
2025-05-16 22:30:45,338 [INFO] __main__ - !!! MONİTÖR: Message sender thread tamamlandı !!! (main.py:168)
2025-05-16 22:30:45,338 [INFO] __main__ - MONİTÖR: active_thread None olarak ayarlandı (main.py:172)
2025-05-16 22:30:48,354 [INFO] __main__ - MONİTÖR: Tam döngü tamamlandı, scraper'ı tekrar başlatıyorum (main.py:179)
2025-05-16 22:30:48,354 [INFO] __main__ - Önceki scraper thread temizlendi (main.py:240)
2025-05-16 22:30:48,355 [INFO] scraper_thread - Chrome işlemleri temizleniyor... (scraper_thread.py:261)
2025-05-16 22:30:48,355 [INFO] scraper_thread - Chrome işlemleri sonlandırılıyor... (scraper_thread.py:261)
2025-05-16 22:30:50,682 [INFO] scraper_thread - Chrome işlemleri başarıyla sonlandırıldı (scraper_thread.py:261)
2025-05-16 22:30:54,022 [INFO] scraper_thread - TikTok Live sayfasına gidiliyor... (scraper_thread.py:261)
2025-05-16 22:31:06,982 [INFO] scraper_thread - Scraper başladı, süre: 1 dk (scraper_thread.py:261)
2025-05-16 22:31:11,587 [INFO] scraper_thread - temmuzk | 4 (scraper_thread.py:261)
2025-05-16 22:31:11,595 [INFO] scraper_thread - ✅ temmuzk veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:261)
2025-05-16 22:31:20,521 [INFO] scraper_thread - zehraakkus.x | 34 (scraper_thread.py:261)
2025-05-16 22:31:20,524 [INFO] scraper_thread - ✅ zehraakkus.x veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:261)
2025-05-16 22:31:25,890 [INFO] scraper_thread - xtugcee1 | 7 (scraper_thread.py:261)
2025-05-16 22:31:25,894 [INFO] scraper_thread - ✅ xtugcee1 veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:261)
2025-05-16 22:31:29,823 [INFO] scraper_thread - senpaiimelisa | 186 (scraper_thread.py:261)
2025-05-16 22:31:29,831 [INFO] scraper_thread - ✅ senpaiimelisa veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:261)
2025-05-16 22:31:34,905 [INFO] scraper_thread - bektas63 | 2 (scraper_thread.py:261)
2025-05-16 22:31:34,909 [INFO] scraper_thread - ✅ bektas63 veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:261)
2025-05-16 22:31:38,968 [INFO] scraper_thread - sebos.cilek3444 | 4 (scraper_thread.py:261)
2025-05-16 22:31:38,972 [INFO] scraper_thread - ✅ sebos.cilek3444 veritabanına kaydedildi (Status: Bekleniyor) (scraper_thread.py:261)
2025-05-16 22:31:40,624 [INFO] __main__ - Otomasyon durdurma sinyali gönderildi (main.py:206)
2025-05-16 22:31:40,627 [INFO] __main__ - Scraper thread durduruldu (main.py:211)
2025-05-16 22:31:40,627 [INFO] StatusChecker - ⏹ StatusChecker durdurma sinyali alındı. (status_checker.py:87)
2025-05-16 22:31:40,628 [INFO] __main__ - Status checker thread durduruldu (main.py:215)
2025-05-16 22:31:40,629 [INFO] MessageSender - ⏹ MessageSender durdurma sinyali alındı. (message_sender_thread.py:369)
2025-05-16 22:31:40,630 [INFO] __main__ - Message sender thread durduruldu (main.py:219)
2025-05-16 22:31:40,630 [INFO] __main__ - Tüm thread'ler durduruldu (main.py:221)
