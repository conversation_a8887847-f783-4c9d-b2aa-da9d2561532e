import os
import re
import random
import time
import logging
import subprocess
import psutil
import threading
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
urllib3.disable_warnings(urllib3.exceptions.NewConnectionError)
urllib3.disable_warnings(urllib3.exceptions.MaxRetryError)

from typing import Tuple, Optional, List
from datetime import datetime
from pathlib import Path
from selenium import webdriver
from selenium.common.exceptions import WebDriverException

from constants import config_manager

logger: logging.Logger = logging.getLogger('Utils')

# Global thread senkronizasyonu için
thread_lock = threading.Lock()
chrome_semaphore = threading.Semaphore(1)
thread_status = {
    'scraper_running': False,
    'status_checker_running': False,
    'message_sender_running': False,
    'last_active_thread': None
}

def get_thread_status():
    """Thread durumlarını döndürür"""
    with thread_lock:
        return thread_status.copy()

def set_thread_status(thread_name, is_running):
    """Thread durumunu günceller"""
    with thread_lock:
        if thread_name == 'scraper':
            thread_status['scraper_running'] = is_running
            if is_running:
                thread_status['last_active_thread'] = 'scraper'
        elif thread_name == 'status_checker':
            thread_status['status_checker_running'] = is_running
            if is_running:
                thread_status['last_active_thread'] = 'status_checker'
        elif thread_name == 'message_sender':
            thread_status['message_sender_running'] = is_running
            if is_running:
                thread_status['last_active_thread'] = 'message_sender'
                
def wait_for_threads_to_finish(current_thread_name, logger_func=None):
    """Diğer thread'lerin bitmesini bekler"""
    if logger_func:
        logger_func(f"{current_thread_name} diğer thread'lerin bitmesini bekliyor...")
    
    max_wait = 60  # maksimum bekleme süresi (saniye)
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        status = get_thread_status()
        other_threads_running = False
        
        if current_thread_name == 'scraper':
            other_threads_running = status['status_checker_running'] or status['message_sender_running']
        elif current_thread_name == 'status_checker':
            other_threads_running = status['scraper_running'] or status['message_sender_running']
        elif current_thread_name == 'message_sender':
            other_threads_running = status['scraper_running'] or status['status_checker_running']
            
        if not other_threads_running:
            if logger_func:
                logger_func(f"Tüm diğer thread'ler tamamlandı, {current_thread_name} başlayabilir.")
            return True
            
        time.sleep(1)
        
    if logger_func:
        logger_func(f"Maksimum bekleme süresi aşıldı, {current_thread_name} yine de başlayacak.")
    return False

def parse_range(range_str: str) -> Tuple[int, int]:
    """
    Parses a range string.
    
    For example:
      "100+" returns (100, -1)
      "50-100" returns (50, 100)
    
    Args:
        range_str: The range string to parse.
    
    Returns:
        A tuple containing the lower bound and upper bound. Upper bound is -1 if open-ended.
    """
    try:
        range_str = range_str.strip()
        match = re.match(r"(\d+)\+", range_str)
        if match:
            return int(match.group(1)), -1
        parts = range_str.split('-')
        if len(parts) == 2 and parts[0].isdigit() and parts[1].isdigit():
            return int(parts[0]), int(parts[1])
    except Exception as e:
        logger.error(f"Range parsing error: {e}")
    return (0, -1)

def parse_viewer_count(raw_text: Optional[str]) -> int:
    """
    Converts a viewer count string to an integer.
    Supports abbreviations like 'k' and 'm'.
    Handles Turkish formats like '8 kişi izliyor', '1,2k izleyici', etc.
    """
    if not raw_text:
        return 0
    try:
        txt = raw_text.lower().strip()
        # Türkçe son ekleri ve gereksiz karakterleri temizle
        for suffix in [" izleyici", " kişi izliyor", " kişi", " izliyor"]:
            if txt.endswith(suffix):
                txt = txt[:-len(suffix)]
        txt = txt.replace(",", ".").replace(" ", "").strip()
        if 'm' in txt:
            txt = txt.replace('m', '')
            return int(float(txt) * 1_000_000)
        if 'k' in txt:
            txt = txt.replace('k', '')
            return int(float(txt) * 1_000)
        return int(float(txt))
    except Exception as e:
        logger.error(f"Viewer count parsing error: {e} (raw_text: {raw_text})")
        return 0

def random_sleep(min_sec: float = 2, max_sec: float = 5) -> float:
    """
    Sleeps for a random duration between min_sec and max_sec seconds.
    
    Args:
        min_sec: Minimum seconds to sleep.
        max_sec: Maximum seconds to sleep.
    
    Returns:
        The duration of sleep in seconds.
    """
    delay = random.uniform(min_sec, max_sec)
    time.sleep(delay)
    return delay

def kill_chrome_processes(log_fn=logger.info, force=False) -> bool:
    """
    Chrome tarayıcısının tüm işlemlerini sonlandırır.
    
    Args:
        log_fn: Log fonksiyonu (isteğe bağlı)
        force: Zorla sonlandırma (True) veya nazik sonlandırma (False)
    
    Returns:
        bool: Tüm işlemlerin başarıyla sonlandırılıp sonlandırılmadığı
    """
    try:
        # Thread durumunu kontrol et
        status = get_thread_status()
        
        # Eğer başka thread'ler Chrome kullanıyorsa ve zorla sonlandırma istenmediyse uyarı ver
        if not force:
            if status['scraper_running'] or status['status_checker_running'] or status['message_sender_running']:
                active_threads = []
                if status['scraper_running']: active_threads.append("Scraper")
                if status['status_checker_running']: active_threads.append("Status Checker")
                if status['message_sender_running']: active_threads.append("Message Sender")
                
                log_fn(f"⚠ UYARI: Aktif thread'ler var: {', '.join(active_threads)}. Chrome süreçleri sonlandırılmıyor!")
                return False
        
        # Önce tüm Chrome ve ChromeDriver PID'lerini bul
        chrome_pids = []
        chromedriver_pids = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                proc_name = proc.info['name'].lower()
                if 'chrome' in proc_name:
                    # Eğer chrome.exe'nin komut satırı parametrelerini alabilirsek
                    try:
                        cmdline = proc.cmdline()
                        # Test sürümü Chrome'u kontrol et
                        if any('chrome-win64' in cmd.lower() for cmd in cmdline):
                            chrome_pids.append(proc.info['pid'])
                    except:
                        # Komut satırı alınamazsa sadece ismine göre ekle
                        chrome_pids.append(proc.info['pid'])
                elif 'chromedriver' in proc_name:
                    chromedriver_pids.append(proc.info['pid'])
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
        
        # Windows'ta işlem sonlandırma
        if os.name == 'nt':
            # Önce nazik bir şekilde kapanmayı dene
            try:
                for pid in chrome_pids:
                    subprocess.run(['taskkill', '/PID', str(pid)], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, timeout=3)
                for pid in chromedriver_pids:
                    subprocess.run(['taskkill', '/PID', str(pid)], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, timeout=3)
                time.sleep(2)
            except:
                pass
                
            # Hala çalışıyorsa zorla sonlandır
            try:
                for pid in chrome_pids:
                    subprocess.run(['taskkill', '/F', '/PID', str(pid)], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, timeout=5)
                for pid in chromedriver_pids:
                    subprocess.run(['taskkill', '/F', '/PID', str(pid)], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, timeout=5)
            except:
                pass
        
        # Son bir kontrol: psutil ile kalan süreçleri öldürmeyi dene
        time.sleep(2)
        still_running = []
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                proc_name = proc.info['name'].lower()
                if ('chrome' in proc_name and proc.info['pid'] in chrome_pids) or \
                   ('chromedriver' in proc_name and proc.info['pid'] in chromedriver_pids):
                    still_running.append(proc_name)
                    try:
                        proc.kill()
                    except:
                        pass
            except:
                pass
        
        success = len(still_running) == 0
        if not success:
            log_fn(f"Bazı Chrome işlemleri hala çalışıyor: {still_running}")
        else:
            log_fn("Tüm Chrome işlemleri başarıyla sonlandırıldı.")
        
        return success
    except Exception as e:
        log_fn(f"Chrome işlemlerini sonlandırma hatası: {e}")
        return False

_driver_pool = None

def get_shared_driver(headless=False):
    global _driver_pool
    if _driver_pool is None or not _driver_pool.is_alive():
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        options = Options()
        if headless:
            options.add_argument("--headless=new")
        options.add_argument(f"user-data-dir={config_manager.paths.CHROME_PROFILE_PATH}")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        
        service = Service(config_manager.paths.CHROME_DRIVER_PATH)
        _driver_pool = webdriver.Chrome(service=service, options=options)
    
    return _driver_pool
    

def kill_firefox_processes(log_func):
    log_func("Firefox işlemleri sonlandırılıyor...")
    killed_any = False
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            if 'firefox' in proc.info['name'].lower():
                log_func(f"Sonlandırılıyor: {proc.info['name']} (PID: {proc.info['pid']})")
                try:
                    proc.terminate() # Önce nazikçe sonlandırmayı dene
                    killed_any = True
                except psutil.NoSuchProcess:
                    log_func(f"PID {proc.info['pid']} zaten sonlanmış.")
                    continue
                except Exception as e_term:
                    log_func(f"PID {proc.info['pid']} sonlandırılamadı (terminate): {e_term}. kill denenecek.")
                    try:
                        proc.kill() # Sonra zorla
                        killed_any = True
                    except Exception as e_kill:
                        log_func(f"PID {proc.info['pid']} zorla da sonlandırılamadı (kill): {e_kill}")
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass # İşlem zaten yoksa veya erişim engellenmişse devam et

    if killed_any:
        log_func("Firefox işlemleri sonlandırıldı. Kontrol için 2sn bekleniyor...")
        time.sleep(2) # İşlemlerin tamamen kapanması için biraz bekle
    else:
        log_func("Çalışan Firefox işlemi bulunamadı.")

    # Windows'ta taskkill ile ek temizlik (isteğe bağlı, daha agresif)
    try:
        subprocess.run(["taskkill", "/F", "/IM", "firefox.exe"], check=False, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        log_func("taskkill komutu Firefox için çalıştırıldı (ekstra temizlik).")
    except FileNotFoundError:
        log_func("taskkill komutu bulunamadı (Windows'a özgü).")
    except Exception as e_taskkill:
        log_func(f"taskkill komutu çalıştırılırken hata: {e_taskkill}")

def enhanced_log(logger_func, thread_name, message, level=logging.INFO, show_in_ui=True):
    """
    Gelişmiş loglama fonksiyonu - thread adı ve zaman damgası ekler
    
    Args:
        logger_func: Log fonksiyonu
        thread_name: Thread adı
        message: Log mesajı
        level: Log seviyesi
        show_in_ui: UI'da gösterilsin mi
    """
    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
    formatted_message = f"[{timestamp}] [{thread_name}] {message}"
    
    if logger_func:
        logger_func(formatted_message, level, show_in_ui)
    else:
        logger.log(level, formatted_message)
    
def setup_standard_chrome_driver(
    driver_path: str, 
    profile_path: str, 
    profile_dir: str, 
    binary_path: str, 
    headless: bool = False,
    log_path: str = None,
    log_func = None
) -> Optional[webdriver.Chrome]:
    """
    Standart Chrome sürücüsü ayarlarını yapar ve sürücüyü başlatır.
    """
    try:
        options = webdriver.ChromeOptions()
        
        # Temel yol ayarları
        options.binary_location = binary_path
        options.add_argument(f"--user-data-dir={profile_path}")
        options.add_argument(f"--profile-directory={profile_dir}")
        
        # Performans ve stabilite ayarları
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-notifications")
        options.add_argument("--disable-popup-blocking")
        options.add_argument("--start-maximized")
        options.add_argument("--lang=tr-TR")
        
        # Otomasyon tespitini engelleme
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
        options.add_experimental_option("useAutomationExtension", False)
        
        # Headless mod ayarları
        if headless:
            options.add_argument("--headless=new")
            options.add_argument("--window-size=1920,1080")
        
        # Log ayarları
        if log_path:
            options.add_argument(f"--log-path={log_path}")
        
        # ChromeDriver servisini başlat
        service = webdriver.chrome.service.Service(
            executable_path=driver_path,
            log_path=log_path if log_path else "NUL"
        )
        
        # Chrome sürücüsünü başlat
        driver = webdriver.Chrome(service=service, options=options)
        
        # Otomasyon belirteçlerini gizle
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # Sayfa yükleme zaman aşımı
        driver.set_page_load_timeout(30)
        
        if log_func:
            log_func("Chrome sürücüsü başarıyla başlatıldı")
        
        return driver
        
    except Exception as e:
        if log_func:
            log_func(f"Chrome sürücüsü başlatılırken hata: {str(e)}", level=logging.ERROR)
        return None
    
def check_memory_usage(log_func=None, threshold_mb=500):
    """
    Sistemin bellek kullanımını kontrol eder ve gerekirse uyarı verir
    
    Args:
        log_func: Log fonksiyonu
        threshold_mb: Uyarı eşiği (MB)
        
    Returns:
        bool: Bellek kullanımı eşiğin altındaysa True
    """
    try:
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        memory_usage_mb = memory_info.rss / 1024 / 1024  # Byte -> MB
        
        if log_func:
            log_func(f"Bellek kullanımı: {memory_usage_mb:.2f} MB")
            
        if memory_usage_mb > threshold_mb:
            if log_func:
                log_func(f"⚠ Bellek kullanımı eşiği aşıldı: {memory_usage_mb:.2f} MB > {threshold_mb} MB", level=logging.WARNING)
            return False
            
        return True
    except Exception as e:
        if log_func:
            log_func(f"Bellek kullanımı kontrolü sırasında hata: {e}", level=logging.ERROR)
        return True
    
class SemaphoreContext:
    """
    Semaphore için context manager sınıfı
    with bloğu içinde kullanılır ve blok sonunda otomatik olarak serbest bırakılır
    
    Kullanım:
        with SemaphoreContext(semaphore, log_func, "Thread adı"):
            # Semaphore alınmış durumda
            # İşlemler...
        # Semaphore otomatik olarak serbest bırakılır
    """
    def __init__(self, semaphore, log_func=None, thread_name=""):
        self.semaphore = semaphore
        self.log_func = log_func
        self.thread_name = thread_name
        
    def __enter__(self):
        if self.log_func:
            self.log_func(f"[{self.thread_name}] Semaphore alınıyor...")
        self.semaphore.acquire()
        if self.log_func:
            self.log_func(f"[{self.thread_name}] Semaphore alındı.")
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.log_func:
            self.log_func(f"[{self.thread_name}] Semaphore serbest bırakılıyor...")
        self.semaphore.release()
        if self.log_func:
            self.log_func(f"[{self.thread_name}] Semaphore serbest bırakıldı.")
    
class SharedData:
    """
    Thread'ler arası veri paylaşımı için güvenli bir mekanizma
    """
    def __init__(self):
        self._data_lock = threading.Lock()
        self._data = {}
        
    def set(self, key, value):
        """Veri ekler veya günceller"""
        with self._data_lock:
            self._data[key] = value
            
    def get(self, key, default=None):
        """Veri okur"""
        with self._data_lock:
            return self._data.get(key, default)
            
    def delete(self, key):
        """Veri siler"""
        with self._data_lock:
            if key in self._data:
                del self._data[key]
                
    def clear(self):
        """Tüm verileri siler"""
        with self._data_lock:
            self._data.clear()
            
    def get_all(self):
        """Tüm verilerin bir kopyasını döndürür"""
        with self._data_lock:
            return self._data.copy()

# Global paylaşılan veri nesnesi
shared_data = SharedData()
    