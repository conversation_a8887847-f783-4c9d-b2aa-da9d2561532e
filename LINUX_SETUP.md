# TikTok Scraper - Linux AlmaLinux 9 Kurulum Rehberi

## 🚀 Hızlı Başlangıç

### 1. Sistem Ha<PERSON>ı<PERSON>ı<PERSON>ı

```bash
# Sistem güncellemesi
sudo dnf update -y

# Gerekli paketleri kur
sudo dnf install python3 python3-pip git wget curl -y

# Chrome/Chromium kurulumu
sudo dnf install chromium chromium-headless -y

# ChromeDriver kurulumu
sudo dnf install chromedriver -y

# MySQL client (uzak MySQL için)
sudo dnf install mysql -y
```

### 2. <PERSON>je <PERSON>

```bash
# Proje dizinine git
cd /path/to/your/project

# Virtual environment oluştur
python3 -m venv venv

# Virtual environment'ı aktifleştir
source venv/bin/activate

# Bağımlılıkları kur
pip install -r requirements.txt
```

### 3. Konfigürasyon

```bash
# .env dosyasını oluştur
cp .env.example .env

# .env dosyasını düzenle
nano .env
```

### 4. Veritabanı Ayarları

MySQL veritabanınızın hazır olduğundan emin olun:

```sql
CREATE DATABASE tiktok_live_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 5. Çalıştırma

```bash
# Script'i çalıştırılabilir yap
chmod +x start_automation.sh

# Automation'ı başlat
./start_automation.sh
```

## 🔧 Manuel Çalıştırma

```bash
# Virtual environment'ı aktifleştir
source venv/bin/activate

# Ana programı çalıştır
python3 main.py start 5 headless

# Veya automation worker'ı çalıştır
python3 automation_worker.py
```

## 🐛 Sorun Giderme

### Chrome/Chromium Sorunları

```bash
# Chromium versiyonunu kontrol et
chromium-browser --version

# ChromeDriver versiyonunu kontrol et
chromedriver --version

# Chrome işlemlerini temizle
pkill -f chrome
pkill -f chromium
pkill -f chromedriver
```

### Python Bağımlılık Sorunları

```bash
# Pip'i güncelle
pip install --upgrade pip

# Bağımlılıkları yeniden kur
pip install --force-reinstall -r requirements.txt
```

### Veritabanı Bağlantı Sorunları

```bash
# MySQL servisini kontrol et
sudo systemctl status mysqld

# MySQL'e bağlanmayı test et
mysql -h localhost -u root -p
```

## 📝 Önemli Notlar

1. **Headless Mode**: Linux sunucuda GUI olmadığı için `headless=True` kullanın
2. **Permissions**: Script'lerin çalıştırılabilir olduğundan emin olun
3. **Firewall**: Gerekli portların açık olduğundan emin olun
4. **Memory**: Chrome yoğun bellek kullanır, sunucunuzun yeterli RAM'i olduğundan emin olun

## 🔄 Servis Olarak Çalıştırma

Systemd servisi oluşturmak için:

```bash
sudo nano /etc/systemd/system/tiktok-automation.service
```

```ini
[Unit]
Description=TikTok Automation Service
After=network.target

[Service]
Type=simple
User=your_username
WorkingDirectory=/path/to/your/project
Environment=PATH=/path/to/your/project/venv/bin
ExecStart=/path/to/your/project/venv/bin/python automation_worker.py
Restart=always

[Install]
WantedBy=multi-user.target
```

```bash
# Servisi etkinleştir
sudo systemctl enable tiktok-automation
sudo systemctl start tiktok-automation
sudo systemctl status tiktok-automation
```
