import time
import json
import pymysql
import logging
from main import MainApp
from dotenv import load_dotenv
import os
import traceback
import sys
import threading
import subprocess

# .env dosyasını yükle
load_dotenv()

DB_CONFIG = {
    "host": os.getenv("DB_HOST", os.getenv("MYSQL_HOST", "localhost")),
    "user": os.getenv("DB_USER", os.getenv("MYSQL_USER", "root")),
    "password": os.getenv("DB_PASSWORD", os.getenv("MYSQL_PASSWORD", "")),
    "database": os.getenv("DB_NAME", os.getenv("MYSQL_DATABASE", "tiktok_live_data")),
    "charset": "utf8mb4",
    "cursorclass": pymysql.cursors.DictCursor
}

# Basit loglama ayarları - DUPLIKASYON ÖNLEME
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.FileHandler("automation_worker.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Global değişkenler
main_app_lock = threading.Lock()
global_main_app = None

def get_db_connection():
    """Veritabanı bağlantısı oluşturur ve test eder"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        # Bağlantıyı test et
        with conn.cursor() as cursor:
            cursor.execute("SELECT 1")
        return conn
    except Exception as e:
        logger.error(f"❌ Veritabanı bağlantı hatası: {e}")
        raise

def update_status(status, last_error=None):
    """Otomasyon durumunu günceller"""
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "UPDATE automation_status SET last_heartbeat=NOW(), status=%s, last_error=%s WHERE id=1",
                    (status, last_error)
                )
            conn.commit()
    except Exception as e:
        logger.error(f"❌ Durum güncelleme hatası: {e}")

def force_close_all_chrome():
    """Tüm Chrome işlemlerini zorla kapatır - PLATFORM AWARE"""
    try:
        logger.info("🔄 Tüm Chrome işlemleri kapatılıyor...")
        import subprocess
        import platform

        if platform.system() == "Windows":
            # Chrome işlemlerini kapat
            subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'],
                         capture_output=True, text=True)
            # ChromeDriver işlemlerini kapat
            subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'],
                         capture_output=True, text=True)
        else:  # Linux/Unix
            # Chrome işlemlerini kapat
            subprocess.run(['pkill', '-f', 'chrome'], capture_output=True)
            subprocess.run(['pkill', '-f', 'chromium'], capture_output=True)
            subprocess.run(['pkill', '-f', 'chromedriver'], capture_output=True)

        # İşlemlerin tamamen kapanması için bekle
        time.sleep(3)
        logger.info("✅ Tüm Chrome işlemleri kapatıldı")

    except Exception as e:
        logger.warning(f"⚠️ Chrome kapatma sırasında hata: {e}")

def cleanup_main_app():
    """MainApp instance'ını güvenli şekilde temizler"""
    global global_main_app
    try:
        if global_main_app:
            logger.info("🧹 Otomasyon temizleniyor...")
            if hasattr(global_main_app, 'cycle_running') and global_main_app.cycle_running:
                global_main_app.stop_automation()
                time.sleep(2)
            global_main_app = None
            logger.info("✅ Otomasyon temizlendi")
    except Exception as e:
        logger.error(f"❌ Temizleme hatası: {e}")
        global_main_app = None

def process_command(cmd):
    """Komutları işler - BASITLEŞTIRILMIŞ VERSİYON"""
    global global_main_app

    try:
        with main_app_lock:  # Thread-safe işlem
            command = cmd['command']
            params = cmd.get('params')
            params_dict = json.loads(params) if params else {}

            if command == 'start':
                duration = params_dict.get('duration', 1)  # Varsayılan 1 dakika
                headless = params_dict.get('headless', False)
                message_mode = params_dict.get('messageMode', 'both')

                # Önceki instance'ı kontrol et
                if global_main_app and hasattr(global_main_app, 'cycle_running') and global_main_app.cycle_running:
                    logger.info("⚠️ Otomasyon zaten çalışıyor")
                    return global_main_app

                # Temizlik işlemleri
                cleanup_main_app()
                force_close_all_chrome()  # Tüm Chrome işlemlerini kapat

                # Başlatma logları - TEK SEFER
                logger.info("🚀 TikTok Otomasyon Sistemi başlatılıyor...")
                logger.info(f"⏱️ Döngü süresi: {duration} dakika")

                # Veritabanı bağlantısını test et
                try:
                    conn = get_db_connection()
                    conn.close()
                    logger.info("✅ Veritabanına bağlandı")
                except Exception as db_error:
                    logger.error(f"❌ Veritabanı bağlantı hatası: {db_error}")
                    update_status('error', f'Veritabanı hatası: {db_error}')
                    return None

                # Yeni instance oluştur
                global_main_app = MainApp()
                global_main_app.set_duration(duration)
                global_main_app.set_headless(headless)
                global_main_app.set_message_mode(message_mode)

                # Otomasyonu başlat
                success = global_main_app.start_automation()
                if success:
                    update_status('running')
                    logger.info(f"✅ Otomasyon başlatıldı (ID: {cmd['id']})")
                else:
                    logger.error("❌ Otomasyon başlatılamadı")
                    cleanup_main_app()
                    update_status('error', 'Başlatma hatası')

            elif command == 'stop':
                if global_main_app and hasattr(global_main_app, 'cycle_running') and global_main_app.cycle_running:
                    logger.info("⏹️ Otomasyon durduruluyor...")
                    global_main_app.stop_automation()
                    cleanup_main_app()
                    force_close_all_chrome()  # Chrome'u temizle
                    update_status('stopped')
                    logger.info("✅ Otomasyon durduruldu")
                else:
                    logger.info("ℹ️ Otomasyon zaten durdurulmuş")

            return global_main_app

    except Exception as e:
        logger.error(f"❌ Komut işleme hatası: {e}")
        logger.error(traceback.format_exc())
        update_status('error', str(e))
        return global_main_app

def ensure_database_schema():
    """Veritabanı şemasının güncel olduğundan emin olur"""
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                # params alanının LONGTEXT olduğundan emin ol
                cursor.execute("""
                    ALTER TABLE automation_commands
                    MODIFY COLUMN params LONGTEXT
                """)
                logger.info("✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502)")
    except Exception:
        # Hata varsa da devam et, muhtemelen zaten doğru tipte
        pass

def main_loop():
    """Ana döngü - BASITLEŞTIRILMIŞ VERSİYON"""
    global global_main_app
    consecutive_errors = 0
    max_consecutive_errors = 5

    # Başlangıç kontrolleri
    ensure_database_schema()
    logger.info("✅ Sistem hazır - komutlar bekleniyor")

    while True:
        try:
            with get_db_connection() as conn:
                with conn.cursor() as cursor:
                    # Bekleyen komutları kontrol et
                    cursor.execute("""
                        SELECT * FROM automation_commands
                        WHERE status='pending'
                        ORDER BY created_at ASC
                        LIMIT 1
                    """)
                    cmd = cursor.fetchone()

                    if cmd:
                        logger.info(f"📨 Komut: {cmd['command']} (ID: {cmd['id']})")
                        process_command(cmd)

                        # Komut tamamlandı olarak işaretle
                        cursor.execute("""
                            UPDATE automation_commands
                            SET status='done', processed_at=NOW()
                            WHERE id=%s
                        """, (cmd['id'],))
                        conn.commit()

                        consecutive_errors = 0  # Başarılı işlem

            # Sistem durumunu güncelle
            if global_main_app is None:
                update_status('idle')
            elif hasattr(global_main_app, 'cycle_running') and global_main_app.cycle_running:
                update_status('running')
            else:
                update_status('idle')

        except Exception as e:
            consecutive_errors += 1
            logger.error(f"❌ Sistem hatası: {e}")
            update_status('error', str(e))

            # Çok fazla hata varsa uzun bekleme
            if consecutive_errors >= max_consecutive_errors:
                logger.warning(f"⚠️ Çok fazla hata ({consecutive_errors}), 30 saniye bekleniyor...")
                time.sleep(30)
                consecutive_errors = 0
            else:
                time.sleep(2)
        else:
            time.sleep(2)  # Normal bekleme

if __name__ == "__main__":
    try:
        logger.info("🚀 TikTok Otomasyon Worker başlatılıyor...")
        logger.info(f"📁 Dizin: {os.getcwd()}")

        # Veritabanı bağlantısını test et
        try:
            conn = get_db_connection()
            conn.close()
            logger.info("✅ Veritabanı bağlantısı başarılı")
        except Exception as db_error:
            logger.error(f"❌ Veritabanı bağlantı hatası: {db_error}")
            sys.exit(1)

        main_loop()

    except KeyboardInterrupt:
        logger.info("⏹️ Program durduruldu")
        cleanup_main_app()
        force_close_all_chrome()
    except Exception as e:
        logger.error(f"❌ Kritik hata: {e}")
        cleanup_main_app()
        force_close_all_chrome()
    finally:
        logger.info("🔚 Program sonlandırıldı")