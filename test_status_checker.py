#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import logging

# Loglama ayarla
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

try:
    logger.info("🔧 Status Checker test başlıyor...")
    
    # Import'ları test et
    from database_manager import DatabaseManager
    from status_checker import StatusCheckerThread
    logger.info("✅ Import'lar başarılı")
    
    # Database manager oluştur
    db_manager = DatabaseManager()
    logger.info("✅ DatabaseManager oluşturuldu")
    
    # Test kullanıcıları
    test_publishers = [
        {"username": "test_user1"},
        {"username": "test_user2"}
    ]
    
    logger.info(f"🔧 StatusCheckerThread oluşturuluyor... ({len(test_publishers)} kullanıcı)")
    
    # Status checker oluştur
    status_checker = StatusCheckerThread(
        db_manager=db_manager,
        publishers=test_publishers,
        parent=None
    )
    
    logger.info("✅ StatusCheckerThread oluşturuldu")
    logger.info("🚀 StatusCheckerThread başlatılıyor...")
    
    # Thread'i başlat
    status_checker.start()
    logger.info("✅ StatusCheckerThread başlatıldı")
    
    # Kısa süre bekle
    import time
    time.sleep(5)
    
    logger.info("✅ Test tamamlandı")
    
except Exception as e:
    logger.error(f"❌ Test hatası: {e}")
    import traceback
    logger.error(f"❌ Hata detayı: {traceback.format_exc()}")
